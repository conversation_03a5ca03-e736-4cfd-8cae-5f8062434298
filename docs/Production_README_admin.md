# Admin Quiz Setup Instructions

## 1. Move to the Location of the Binary File

Navigate to the folder where you downloaded the admin binary file (`herbit-admin` or the legacy `quiz_admin`). Using `herbit-admin` (the Go TUI) is recommended.

---

## 2. Open Terminal

Open a terminal in the same directory where the admin binary file is located.

---

## 3. Grant Execute Permission

Provide execution permissions to the admin binary file (example for `herbit-admin`):

```bash
chmod +x herbit-admin
```

---

## 4. Execute the Admin Quiz File

Run the admin quiz file using the following command (example for `herbit-admin`):

```bash
./herbit-admin
```

---

## 5. Authenticate and Choose an Action

If you are an authenticated user, you will see a menu with options. The primary workflow involves:

### I. Skills Management

- **Create a Skill**: Define skills with names and detailed descriptions. The description is vital as it forms the basis for question content.
- **List Skills**: View existing skills.
- **Map Skill to Assessment**: Link a skill (for metadata) to an assessment.

### II. Assessments & Questions Management

This is a multi-step process:

**A. Generate Question Set:**

1.  Select this option from the menu.
2.  **Enter Question Set Identifier**: Provide a unique name for this batch of questions (e.g., `DevOps_Fundamentals_Q1_2024`). This identifier will link these questions to an assessment later.
3.  **Select a Skill**: Choose a skill from the list. The description of this skill will be used by the AI to generate questions.
4.  The system will generate questions and save them. A CSV file containing these questions (e.g., `questions_DevOps_Fundamentals_Q1_2024.csv`) will be created in the current directory. This file is important for the next steps.

**B. Create Assessments (Mock & Final):**

1.  Select this option from the menu.
2.  **Enter User-Defined Assessment Name**: Give your assessment a recognizable name (e.g., `DevOps Midterm Exam`).
3.  **Enter Question Set Identifier to use**: Input the identifier you created in step A (e.g., `DevOps_Fundamentals_Q1_2024`). This links the assessment to the generated questions.
4.  **Select Associated Skill(s) (Optional)**: You can link one or more skills to this assessment for categorization/metadata.
5.  **Enter Optional Description**: Add a description for the assessment itself.
6.  The system will create structures for both a Mock and a Final assessment based on your inputs.

**C. Add Final Questions to an Assessment:**

1.  Select this option.
2.  **Enter Question IDs**: Refer to the CSV file generated in step A (e.g., `questions_DevOps_Fundamentals_Q1_2024.csv`). From this file, select the `que_id` values for the questions you want in the final assessment. Enter these IDs as a comma-separated list (e.g., `1,5,10,23,30`).
3.  Ensure your selection meets the minimum difficulty requirements (e.g., 3 easy, 3 intermediate, 4 advanced, based on `FINAL_QUESTION_COUNT` in `.env`). The system will validate this.
4.  These selected questions will now populate the "final" version of the assessment linked to the chosen Question Set Identifier.

### III. Sessions Management

- **Generate Session Codes**: Once assessments are created and (for final assessments) populated with questions:
  1.  Select an assessment (e.g., `DevOps Midterm Exam - Mock Assessment` or `DevOps Midterm Exam - Final Assessment`).
  2.  Enter a comma-separated list of usernames who will take this assessment.
  3.  The system will generate unique 6-digit session codes for each user for that specific assessment.

#### Share the Session Code

Provide the generated session codes to the users. They will use these codes to access their assigned quiz via the `quiz_user` application.

### IV. Reports

- **Generate Reports**:
  - **Date-wise**: Get reports for all assessments taken on a specific date (mock or final).
  - **User-wise**: Get a report of all assessments taken by a specific user.
  - **Question Set-wise**: Get reports for assessments that used a specific "Question Set Identifier" (mock or final type).
- Reports are generated as CSV files (e.g., `final_base_report_(criteria).csv`, `final_score_report_(criteria).csv`) and saved in the current directory. You can open the directory (e.g., `nautilus .` on GNOME) to access them.

---

## Notes

- Ensure that any identifiers or names you enter are accurate and match existing data where required (e.g., Question Set Identifiers).
- The CSV files generated (especially the question set CSV) are important references for subsequent steps like adding final questions.
- Report naming conventions will help you locate the correct files based on the criteria used for generation.
