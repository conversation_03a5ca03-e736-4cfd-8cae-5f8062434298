### Running the `quiz_user` Binary

#### 1. Download `quiz_user`

Download the `quiz_user` file to your local machine.

---

#### 2. Make it Executable

Grant execution permissions to the file:

```bash
chmod +x quiz_user
```

If `quiz_user` runs successfully on your local machine, you can proceed with using it.
If you encounter issues running it locally, follow the next steps to execute it on a virtual machine (VM).

---

### Running `quiz_user` on a Virtual Machine (If Needed)

#### 1. SSH into the VM

Log in to the virtual machine using SSH:

```bash
ssh username@10.0.15.109
```

---

#### 2. <PERSON><PERSON> `quiz_user` to the VM

Transfer the `quiz_user` file from your local machine to the VM's home directory:

```bash
scp quiz_user username@10.0.15.109:~/
```

---

#### 3. Run the File on the VM

Once the file is copied to the VM, execute it:

```bash
./quiz_user
```

---

#### 4. Enter the Quiz Code

Once you have entered the quiz, the system will ask for the quiz code:

```
Please enter Quiz code:
```

---
