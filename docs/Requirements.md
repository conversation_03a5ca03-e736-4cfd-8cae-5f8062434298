# Herbit System Requirements

## Overview

Herbit (Highly Engineered Robot Built for Internal Tests) is a system designed to create and manage assessments for specified skills using Large Language Models (LLMs) to generate multiple-choice questions. The system supports fixed and dynamic question selection, self-service and bulk session creation, and real-time scoring.

## Functional Requirements

### Skill Management

- [x] The system shall allow administrators to define Skills.
- [ ] Each Skill shall have a descriptive summary, optionally suggested by LLMs, which will be used to generate questions.
- [ ] Each Skill shall be associated with multiple questions.
- [x] Questions for each Skill shall be categorized into three difficulty levels: easy, intermediate, and advanced.
- [ ] The system shall display all Skills in a list with:
  - A "Generate Questions" button for each skill
  - A "Question Count" column showing how many questions currently exist for the skill.
- [ ] When "Generate Questions" is clicked:
  - If questions already exist, the system shall use them as context for refining or adding new ones.
  - If no questions exist, it shall generate questions from scratch using the skill's description.
- [ ] No questions shall be generated automatically when assessments are created.

### Assessment Creation and Configuration

- [x] The system shall allow authenticated administrators to create assessments linked to one or more Skills.
- [ ] The system shall support multiple question types including multiple-choice questions, with future support for programming challenges and open-ended questions.
- [ ] The system shall allow administrators to configure assessments to use either:
  - A fixed set of questions (same for every session), or
  - A dynamic pool of questions pulled from associated Skills at session time.
- [x] The system shall enforce a minimum number of questions per difficulty level for each assessment (e.g., 3 easy, 3 intermediate, 4 advanced).
- [x] The system shall store assessment definitions and configurations in the database. (Needs refactoring)
- [x] The system shall store generated questions in a PostgreSQL database with associations to skills and difficulty levels.
- [ ] Administrators shall be able to review and select generated questions for inclusion in assessments.
- [ ] The system shall support assessments with fixed question sets that remain the same for all sessions.
- [ ] The system shall support assessments with dynamically selected questions per session, pulled from associated skills.
- [ ] The system shall allow administrators to configure whether an assessment uses fixed or dynamic questions.

### Session Management

- [ ] The system shall allow sessions to be created through a self-service API using an assessment ID and user email.
- [ ] The system shall allow batch session creation by uploading a list of usernames.
- [ ] Each session shall be assigned a unique 6-digit session code, valid for the duration of the session and reusable after expiration.
- [ ] Each session shall have a set duration defined at creation time.
- [ ] The system shall record the start and end time for each session.

### Assessment Taking

- [x] The system shall allow users to start assessments using a session code.
- [x] The system shall present questions one at a time.
- [ ] The system shall adapt question difficulty based on user performance (correct answers lead to harder questions, incorrect answers lead to easier questions) based on the configured assessment mode.
- [ ] The system shall track user answers in real-time, linking them to the session and specific question.
- [ ] The system shall calculate user scores based on correct vs incorrect answers upon session completion.
- [ ] The system shall provide immediate feedback on answer correctness based on configured assessment settings.
- [ ] The system shall generate a summary report after assessment completion. It should also display it to the user based on configured assessment settings.
- [ ] The system shall display a countdown timer during questions.

### Answer Tracking and Scoring

- [ ] The system shall record each user answer in real-time and associate it with the correct session and question.
- [ ] The system shall calculate the final score based on the number and difficulty of correct answers.
- [ ] The system shall generate a summary report after session completion.

### Reporting

- [ ] The system shall allow administrators to generate user score reports filtered by:
  - Date range
  - User
  - Assessment
  - Skill
- [ ] The system shall provide detailed reports including:
  - User performance metrics
  - Skill-wise success rate
  - Question-wise success rate and difficulty re-adjustment recommendations
  - Skill maps of users based on past assessments
- [x] The system shall export all report types in CSV format.
- [x] The system shall generate a list of all session codes for administrative reference.

## Non-Functional Requirements

### Security

- [x] The system shall authenticate administrators before allowing access to administrative functions. (Current implementation uses a basic check against `ALLOWED_USERS`; full OIDC-based auth is planned).
- [x] The system shall use environment variables for storing sensitive configuration.
- [x] The system shall validate user inputs to prevent injection attacks (Primarily through Pydantic for API and parameterized SQL queries; shell scripts construct JSON for API calls).

### Performance

- [x] The system shall handle multiple concurrent sessions.
- [ ] The system shall respond to user interactions within reasonable time frames (< 200ms).
- [x] The system shall implement retry mechanisms for LLM API calls to handle potential failures.

### Scalability

- [x] The system shall use a containerized deployment architecture.
- [x] The system shall separate application logic from database operations.

### Usability

- [x] The system shall provide clear instructions for both administrators and users.
- [x] The system shall display a countdown timer during quiz questions.
- [x] The system shall provide visual feedback for correct and incorrect answers.

### Configuration

- [x] The system shall allow configuration of:
  - Question counts per assessment
  - Time limits per assessment
  - LLM model selection
  - Database connection parameters

### Compatibility

- [x] The system shall support Linux-based operating systems.
- [x] The system shall provide both a server component and client binaries.

## Technical Requirements

### Database

- [x] The system shall use PostgreSQL for persistent storage.
- [x] The system shall use database migrations to manage schema evolution.
- [x] The database shall store:
  - Skill definitions and descriptions
  - Questions and their difficulty levels, options and correct answers
  - Assessments and configurations
  - User sessions and metadata
  - Session codes and status
  - User responses
  - Reports and logs

### API

- [x] The system shall expose a FastAPI-based REST API for:
  - Skill management
  - Assessment creation and management
  - Session creation and management
  - Question generation and retrieval
  - Answer submission
  - Report generation and export

### Deployment

- [x] The system shall support deployment via Docker and Docker Compose.
- [x] The system shall compile CLI shell scripts into binary executables.

### Integration

- [x] The system shall integrate with external LLM APIs for question generation.
- [ ] The system shall support configuration of API endpoints and credentials.

## Future Goals

- [x] Define and document all user flows and map them to CLI and API interactions.
- [ ] Migrate all CLI tools to Go (Admin CLI is complete; User CLI `quiz_user.sh` is pending migration).
- [ ] Implement full OIDC-based authentication for all APIs.
- [ ] Integrate with ERPNext Training and Recruitment modules to store the results for interviews and internal training assignments.

**Note on CLI Development:**

- Current CLI tools (admin and user) are separate. They will be unified in a Go-based application once full authentication is implemented.
