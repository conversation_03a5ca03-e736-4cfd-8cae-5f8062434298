name: Pre-commit Checks & Linting

on:
  pull_request:
    branches:
      - staging

jobs:
  pre-commit-lint:
    runs-on: vm-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install Python dependencies
        run: pip install pre-commit flake8 black

      - name: Install frontend dependencies
        working-directory: frontend
        run: npm ci

      - name: Run Pre-commit Hooks
        run: pre-commit run --all-files --show-diff-on-failure
