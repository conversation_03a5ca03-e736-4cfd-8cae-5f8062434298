apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: pubsub
spec:
  type: pubsub.redis
  version: v1
  metadata:
    - name: redisHost
      value: redis:6379
    - name: redisPassword
      value: ""
    - name: enableTLS
      value: "false"
    - name: maxRetries
      value: "3"
    - name: maxRetryBackoff
      value: "5s"
    - name: deadLetterTopic
      value: "question-tasks-dlq"
scopes:
  - herbit-api
  - herbit-worker
