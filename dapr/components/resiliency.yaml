apiVersion: dapr.io/v1alpha1
kind: Resiliency
metadata:
  name: worker-resiliency
scopes:
  - herbit-worker
spec:
  policies:
    retries:
      pubsubRetry:
        policy: exponential
        duration: 5s
        maxInterval: 30s
        maxRetries: 3
  targets:
    components:
      pubsub:
        inbound:     # retries on subscription delivery
          retry: pubsubRetry
        outbound:    # retries on publish (if worker also publishes)
          retry: pubsubRetry
