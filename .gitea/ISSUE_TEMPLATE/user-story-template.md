---
name: User Story
about: Template for user stories
title: "User Story"
labels: "user-story"
assignees: ""
---

# User story

As a : [Description of User]

I want : [Functionality]

so that : [Benefit]

# ToDo's List

- [ ] Task 1
- [ ] Task 2
- [ ] Task 3

# Acceptance criteria

Given : [How Things Begin]
When : [Action Taken]
Then : [Output of taking action]

- Feature 1
- Feature 2
- Feature 3

# Sprint Ready Checklist

1.  - [ ] Acceptance criteria defined
2.  - [ ] Team understands acceptance criteria
3.  - [ ] Team has defined solution / steps to satisfy acceptance criteria
4.  - [ ] Acceptance criteria is verifiable / testable
