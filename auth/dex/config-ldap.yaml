issuer: http://dex:5556

storage:
  type: sqlite3
  config:
    file: /tmp/dex.db

web:
  http: 0.0.0.0:5556

# Enable groups claim in ID tokens
oauth2:
  skipApprovalScreen: true
  alwaysShowLoginScreen: false

connectors:
  - type: ldap
    name: OpenLDAP
    id: ldap
    config:
      host: ldap:389 # << docker service name
      insecureNoSSL: true
      bindDN: cn=admin,dc=example,dc=org
      bindPW: admin
      usernamePrompt: Username

      userSearch:
        baseDN: ou=People,dc=example,dc=org
        filter: "(objectClass=person)"
        username: cn # << for login
        idAttr: DN
        emailAttr: mail
        nameAttr: cn

      groupSearch:
        baseDN: ou=Groups,dc=example,dc=org
        filter: "(objectClass=groupOfNames)"
        userMatchers:
          - userAttr: DN
            groupAttr: member
        nameAttr: cn

staticClients:
  - id: example-app
    redirectURIs:
      - "http://localhost:5173/callback"
    name: "Herbit App"
    secret: ZXhhbXBsZS1hcHAtc2VjcmV0
