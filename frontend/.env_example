# API Base URL - Use relative path for development with proxy, full URL for production
VITE_API_BASE_URL=/api
VITE_APP_TITLE=HERBIT Assessment Platform
VITE_AUTH_ENABLED=true

# Dex URLs
VITE_AUTH_CALLBACK_URL=http://localhost:5173/callback
VITE_AUTH_LOGIN_URL=http://127.0.0.1:5556/auth
VITE_AUTH_LOGOUT_URL=http://127.0.0.1:5556/logout
VITE_AUTH_CLIENT_ID=example-app

# Internal Auth Info Fetch (from backend)
VITE_AUTH_USERINFO_URL=http://localhost:8000/api/auth/userinfo

# User Group Names
VITE_ADMIN_GROUP_NAME=admins
VITE_EMPLOYEE_GROUP_NAME=employees

# Specific employee users who should have admin access (comma-separated list of user IDs/emails)
VITE_EMPLOYEE_ADMIN_USERS=

# Frontend Logging Configuration
# LOG_LEVEL: debug, info, warning, error, critical
VITE_LOG_LEVEL=info
VITE_DEBUG=false
VITE_LOG_TO_CONSOLE=true
VITE_LOG_TO_SERVER=false
VITE_LOG_API_REQUESTS=true
VITE_LOG_PERFORMANCE=false
VITE_LOG_USER_ACTIONS=true
