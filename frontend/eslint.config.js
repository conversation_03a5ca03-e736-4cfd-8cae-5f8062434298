// /frontend/eslint.config.js

import globals from "globals";
import pluginJs from "@eslint/js";
import pluginVue from "eslint-plugin-vue";
import vueParser from "vue-eslint-parser";
import babelParser from "@babel/eslint-parser";
import prettierConfig from "eslint-config-prettier";

export default [
  // 1. Global configurations and ignores
  {
    ignores: [
      "dist/",
      "frontend/node_modules/", // Note: Simplified this path. ESLint runs from root, but files are relative.
      "frontend/src/components/ui/**/*",
    ],
  },

  // 2. Base JavaScript rules (from eslint:recommended)
  pluginJs.configs.recommended,

  // 3. Base Vue 3 rules (from eslint-plugin-vue)
  ...pluginVue.configs["flat/recommended"],

  // 4. Your specific configurations and rule overrides for all JS/Vue files
  {
    files: ["**/*.{js,vue}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      // Define the necessary parsers for .vue files and <script> blocks
      parser: vueParser,
      parserOptions: {
        parser: babelParser,
        requireConfigFile: false, // Prevents errors if no babel.config.js is found
      },
      // Define standard browser and Node.js global variables
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    rules: {
      // === CRITICAL RULES: Set to "error" to fail pre-commit hooks ===

      // An unused variable is almost always a mistake or leftover code.
      "no-unused-vars": "error",

      // Leaving console logs in production code is generally undesirable.
      "no-console": "error",

      // Missing default values for props can lead to unexpected runtime behavior.
      "vue/require-default-prop": "error",

      // This was already an error, which is correct.
      "vue/multi-word-component-names": [
        "error",
        {
          ignores: [
            "Header",
            "Footer",
            "Hero",
            "Layout",
            "Login",
            "Sessions",
            "Link",
            "Home",
            "Callback",
            "Alert",
            "Button",
            "Calendar",
            "Card",
            "Dialog",
            "Input",
            "Label",
            "Pagination",
            "Select",
            "Tabs",
            "Toggle",
          ],
        },
      ],

      // === NON-CRITICAL / STYLISTIC RULES: Set to "warn" to be informational ===

      // Using v-html is a security risk, but sometimes intentional. A warning is appropriate.
      "vue/no-v-html": "warn",

      // Long lines are a style issue, not a bug. Prettier will handle this anyway.
      "max-len": "off", // Turned off because Prettier handles it.
      "vue/max-len": "off", // Turned off because Prettier handles it.
    },
  },

  // This must be the LAST item in the array to disable conflicting rules.
  prettierConfig,
];
