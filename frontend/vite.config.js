import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  // Determine proxy target based on environment
  // Use VITE_API_BASE_URL from env, fallback to Docker or localhost
  const isDocker =
    process.env.DOCKER_ENV === "true" || process.env.NODE_ENV === "docker";
  let proxyTarget;

  if (isDocker) {
    // In Docker, always use the backend service name
    proxyTarget = "http://backend:8000";
  } else {
    // For local development, use VITE_API_BASE_URL or localhost
    proxyTarget = env.VITE_API_BASE_URL || "http://localhost:8000";
  }

  // If VITE_API_BASE_URL includes /api, remove it for the proxy target
  // because we'll add the path back in the rewrite
  if (proxyTarget.endsWith("/api")) {
    proxyTarget = proxyTarget.replace(/\/api$/, "");
  }

  return {
    plugins: [vue()],
    server: {
      port: 5173,
      host: true,
      allowedHosts: ["herbit-dev.pride.improwised.dev"],
      proxy: {
        "/api": {
          target: proxyTarget,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => {
            // If using production URL (which includes /api), keep the path as is
            if (
              env.VITE_API_BASE_URL &&
              env.VITE_API_BASE_URL.includes("/api")
            ) {
              return path;
            }
            // For local development and Docker, remove /api prefix
            return path.replace(/^\/api/, "");
          },
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    assetsInclude: ["**/*.svg"],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            "svg-icons": [],
          },
        },
      },
    },
  };
});
