import axios from "axios";
import { logApiRequest, logApiResponse, logApiError } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";

//----------------------------------------------------------------
//  Axios Client Configuration
//----------------------------------------------------------------

// Use the VITE_API_BASE_URL from .env, or fall back to a relative path for proxying.
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "/api";

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30-second timeout for requests
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

//----------------------------------------------------------------
//  Axios Interceptors
//----------------------------------------------------------------

/**
 * Request Interceptor: Prepares every outgoing request.
 * - Logs the request for debugging.
 * - Dynamically attaches the Authorization header if authentication is enabled.
 * - Includes credentials (cookies) for session-based authentication.
 */
apiClient.interceptors.request.use(
  (config) => {
    logApiRequest(
      config.method?.toUpperCase() || "GET",
      config.url,
      config.data,
    );

    const authEnabled = import.meta.env.VITE_AUTH_ENABLED === "true";
    if (authEnabled) {
      const tokenKey = import.meta.env.VITE_AUTH_TOKEN_KEY || "auth_token";
      const token = localStorage.getItem(tokenKey);
      const tokenPrefix = import.meta.env.VITE_AUTH_TOKEN_PREFIX || "Bearer";

      if (token) {
        config.headers.Authorization = `${tokenPrefix} ${token}`;
      }

      // Allow sending cookies to the server, essential for session-based auth.
      // This can be disabled via environment variables if not needed.
      const withCredentials =
        import.meta.env.VITE_AUTH_WITH_CREDENTIALS !== "false";
      if (withCredentials) {
        config.withCredentials = true;
      }
    }

    return config;
  },
  (error) => {
    logApiError("REQUEST", "interceptor", error);
    return Promise.reject(error);
  },
);

/**
 * Response Interceptor: Handles all incoming responses.
 * - Logs successful responses.
 * - Provides centralized error handling for common HTTP statuses (e.g., 401, 403).
 */
apiClient.interceptors.response.use(
  (response) => {
    logApiResponse(
      response.config?.method?.toUpperCase() || "GET",
      response.config?.url || "unknown",
      response.status,
      response.data,
    );
    return response;
  },
  (error) => {
    logApiError(
      error.config?.method?.toUpperCase() || "GET",
      error.config?.url || "unknown",
      error,
    );

    // Centralized handling for specific HTTP error codes.
    if (error.response) {
      const authEnabled = import.meta.env.VITE_AUTH_ENABLED === "true";

      if (authEnabled) {
        // On 401 Unauthorized, redirect the user to the login page.
        if (error.response.status === 401) {
          const loginUrl =
            import.meta.env.VITE_AUTH_LOGIN_URL || `${API_BASE_URL}/auth/login`;
          window.location.href = loginUrl;
        }

        // On 403 Forbidden, redirect to an access denied page if one is configured.
        if (error.response.status === 403) {
          const accessDeniedUrl = import.meta.env.VITE_AUTH_ACCESS_DENIED_URL;
          if (accessDeniedUrl) {
            window.location.href = accessDeniedUrl;
          }
        }
      }
    } else if (error.request) {
      // Handle network errors where the request was made but no response was received.
      logApiError("REQUEST", "no-response", error);
    } else {
      // Handle errors that occurred during request setup.
      logApiError("REQUEST", "setup-error", error);
    }

    return Promise.reject(error);
  },
);

//----------------------------------------------------------------
//  Core Request Logic
//----------------------------------------------------------------

/**
 * A robust, centralized function for making API requests.
 * @param {string} method - The HTTP method (e.g., 'get', 'post').
 * @param {string} url - The API endpoint.
 * @param {Object|null} data - The request payload for 'post', 'put', etc.
 * @param {Object|null} params - The URL parameters for 'get' requests.
 * @param {Object} options - Additional Axios request configuration options.
 * @returns {Promise} An Axios promise.
 */
const apiRequest = (method, url, data = null, params = null, options = {}) => {
  const config = { method, url, ...options };
  if (data) config.data = data;
  if (params) config.params = params;
  return apiClient(config);
};

// Provides convenient, type-safe shortcuts for the `apiRequest` function.
export const request = {
  get: (url, params = null, options = {}) =>
    apiRequest("get", url, null, params, options),
  post: (url, data = null, options = {}) =>
    apiRequest("post", url, data, null, options),
  put: (url, data = null, options = {}) =>
    apiRequest("put", url, data, null, options),
  delete: (url, data = null, options = {}) =>
    apiRequest("delete", url, data, null, options),
  patch: (url, data = null, options = {}) =>
    apiRequest("patch", url, data, null, options),
};

//----------------------------------------------------------------
//  User Info Caching
//----------------------------------------------------------------
let cachedUserInfo = null;
let userInfoPromise = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5-minute cache

/**
 * Clears the cached user information, forcing a refetch on the next request.
 * Dispatches a 'auth-state-changed' event to notify components of the change.
 */
const clearUserInfoCache = () => {
  cachedUserInfo = null;
  userInfoPromise = null;
  lastFetchTime = 0;
  window.dispatchEvent(new CustomEvent("auth-state-changed"));
};

/**
 * Retrieves user information with an intelligent caching strategy.
 * - Returns cached data if it's still fresh.
 * - If a fetch is already in progress, it returns the pending promise to prevent
 *   multiple simultaneous requests for the same data (dog-piling effect).
 * - Uses the native `fetch` API to avoid circular dependencies with axios interceptors.
 * @param {boolean} forceRefresh - If true, bypasses the cache and forces a new API call.
 * @returns {Promise<Object>} A promise that resolves to the user info object.
 */
const getCachedUserInfo = async (forceRefresh = false) => {
  const now = Date.now();

  if (!forceRefresh && cachedUserInfo && now - lastFetchTime < CACHE_DURATION) {
    return cachedUserInfo;
  }

  if (userInfoPromise) {
    return userInfoPromise;
  }

  userInfoPromise = (async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/userinfo`, {
        method: "GET",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        cachedUserInfo = data;
        lastFetchTime = now;
        return data;
      }
      // If the request fails, store a non-authenticated state to prevent repeated failed requests.
      cachedUserInfo = { authenticated: false, user: null };
      lastFetchTime = now;
      return cachedUserInfo;
    } catch (error) {
      logApiError("GET", "/auth/userinfo", error);
      cachedUserInfo = { authenticated: false, user: null };
      lastFetchTime = now;
      return cachedUserInfo;
    } finally {
      // Clear the pending promise to allow new requests after this one completes.
      userInfoPromise = null;
    }
  })();

  return userInfoPromise;
};

//----------------------------------------------------------------
//  API Service Definition
//----------------------------------------------------------------

// The main export containing a structured collection of all API endpoints.
// This is the primary interface for the rest of the application.
export const api = {
  // --- Root Level & Caching ---
  getCachedUserInfo,
  clearUserInfoCache,

  // --- Auth Endpoints ---
  auth: {
    login: () => {
      const loginUrl =
        import.meta.env.VITE_AUTH_LOGIN_URL || `${API_BASE_URL}/auth/login`;
      window.location.href = loginUrl;
    },
    logout: () => {
      const logoutUrl =
        import.meta.env.VITE_AUTH_LOGOUT_URL || `${API_BASE_URL}/auth/logout`;
      window.location.href = logoutUrl;
    },
    getUserInfo: () => {
      const userInfoEndpoint =
        import.meta.env.VITE_AUTH_USERINFO_URL || "/api/auth/userinfo";
      return request.get(userInfoEndpoint);
    },
    getCurrentUserId: () => {
      const userInfoStr = localStorage.getItem("user_info");
      if (!userInfoStr) return null;
      try {
        const userInfo = JSON.parse(userInfoStr);
        return userInfo.id || userInfo.sub || null;
      } catch (e) {
        logApiError("UTIL", "parse-user-info", e);
        return null;
      }
    },
    hasAdminAccess: () => {
      const userInfoStr = localStorage.getItem("user_info");
      if (!userInfoStr) return false;
      try {
        const userInfo = JSON.parse(userInfoStr);
        return hasAdminAccess(userInfo);
      } catch (e) {
        logApiError("UTIL", "check-admin-access", e);
        return false;
      }
    },
  },

  // --- Admin Endpoints ---
  admin: {
    // Assessments
    getAssessments: (params = {}) => request.get("/admin/assessments", params),
    getAssessment: (id) => request.get(`/admin/assessment/${id}`),
    createAssessment: (data) => request.post("/admin/quiz", data),
    getAssessmentQuestions: (id) =>
      request.get(`/admin/assessment-questions/${id}`),
    addFinalQuestions: (data) => request.post("/admin/final-questions", data),

    // Skills
    getSkills: (params = {}) => request.get("/admin/skills", params),
    getSkill: (id) => request.get(`/admin/skills/${id}`),
    createSkill: (data) => request.post("/admin/skills", data),
    suggestSkillDescription: (data) =>
      request.post("/admin/suggest-skill-description", data),
    getSkillQuestions: (skillId) =>
      request.get(`/admin/skill-questions/${skillId}`),
    generateSkillQuestions: (data) =>
      request.post("/admin/generate-skill-questions", data, {
        timeout: 320000, // Long timeout for potentially slow AI generation.
      }),
    getSkillQuestionCounts: () => request.get("/admin/skill-question-counts"),

    // Sessions
    getSessions: (params = {}) => request.get("/admin/sessions", params),
    getSessionDetails: (sessionId) =>
      request.get(`/admin/sessions/${sessionId}/details`),
    getSessionResults: (sessionId) =>
      request.get(`/admin/sessions/${sessionId}/results`),
    createSession: (data) => request.post("/admin/sessions", data),
    getSessionUser: (code) => request.get(`/admin/sessions/${code}/user`),
    getAssessmentsWithSessions: () =>
      request.get("/admin/assessments-with-sessions"),
    generateLink: (data) => request.post("/admin/generate-link", data),

    // Reports
    generateReport: (data) => request.post("/admin/reports", data),
    getUserWiseReport: (params) =>
      request.get("/admin/reports/user-wise", params),
    getSkillwiseHeatmap: () => request.get("/admin/reports/skillwise-heatmap"),
    getAssessmentWiseReport: (params) =>
      request.get("/admin/reports/assessment-wise", params),

    // Users
    getUsers: () => request.get("/admin/users"),
    getUserAssessments: (userId) =>
      request.get(`/admin/users/${userId}/assessments`),
    getUserSkillPerformance: (userId) =>
      request.get(`/admin/users/${userId}/skill-performance`),
  },

  // --- User-Facing Endpoints ---
  user: {
    getUserAssessmentsByEmail: (email) =>
      request.get(`/user/${email}/assessments`),
    getUserSkillPerformanceByEmail: (email) =>
      request.get(`/user/${email}/skill-performance`),
    getUserSessions: (email) =>
      request.get(`/user/${email}/sessions`, null, {
        headers: { "X-Debug": "true" }, // Custom header example for debugging.
      }),
  },

  // --- Quiz Endpoints ---
  quiz: {
    validateSessionCode: (data) => request.post("/validate_session_code", data),
    startSession: (data) => request.post("/start_session", data),
    getQuestions: (sessionCode, params) =>
      request.get(`/get_questions/${sessionCode}`, params),
    checkAndSaveAnswer: (data) => request.post("/check_and_save_answer", data),
    submitSession: (data) => request.post("/submit_session", data),
    pauseSession: (data) => request.post("/pause_session", data),
    resumeSession: (data) => request.post("/resume_session", data),
    getPauseStatus: (sessionCode) =>
      request.get(`/pause_status/${sessionCode}`),
  },
};
