/**
 * Common UI State Composable
 *
 * Provides reusable UI state patterns with:
 * - Loading states
 * - Search functionality
 * - Pagination controls
 * - Tab management
 * - Toggle states
 * - Selection management
 */
import { ref, computed } from "vue";

/**
 * Loading state management
 */
export function useLoadingState(initialState = false) {
  const isLoading = ref(initialState);
  const loadingMessage = ref("");

  const startLoading = (message = "Loading...") => {
    isLoading.value = true;
    loadingMessage.value = message;
  };

  const stopLoading = () => {
    isLoading.value = false;
    loadingMessage.value = "";
  };

  const withLoading = async (asyncFn, message = "Loading...") => {
    startLoading(message);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      stopLoading();
    }
  };

  return {
    isLoading,
    loadingMessage,
    startLoading,
    stopLoading,
    withLoading,
  };
}

/**
 * Search functionality
 */
export function useSearch(options = {}) {
  const { minLength = 2, debounceMs = 300, caseSensitive = false } = options;

  const searchQuery = ref("");
  const isSearching = ref(false);
  const searchResults = ref([]);
  let searchTimeout = null;

  const normalizeQuery = (query) => {
    return caseSensitive ? query : query.toLowerCase();
  };

  const search = (items, searchFn) => {
    const query = searchQuery.value.trim();

    if (!query || query.length < minLength) {
      searchResults.value = [];
      return [];
    }

    const normalizedQuery = normalizeQuery(query);
    const results = items.filter((item) => searchFn(item, normalizedQuery));
    searchResults.value = results;
    return results;
  };

  const debouncedSearch = (items, searchFn) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    isSearching.value = true;
    searchTimeout = setTimeout(() => {
      search(items, searchFn);
      isSearching.value = false;
    }, debounceMs);
  };

  const clearSearch = () => {
    searchQuery.value = "";
    searchResults.value = [];
    isSearching.value = false;

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
  };

  const hasSearchQuery = computed(
    () => searchQuery.value.trim().length >= minLength,
  );
  const hasResults = computed(() => searchResults.value.length > 0);

  return {
    searchQuery,
    isSearching,
    searchResults,
    hasSearchQuery,
    hasResults,
    search,
    debouncedSearch,
    clearSearch,
  };
}

/**
 * Pagination state management
 */
export function usePagination(options = {}) {
  const { itemsPerPage = 10, initialPage = 1 } = options;

  const currentPage = ref(initialPage);
  const totalItems = ref(0);
  const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage));

  const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage);
  const endIndex = computed(() =>
    Math.min(startIndex.value + itemsPerPage, totalItems.value),
  );

  const hasNextPage = computed(() => currentPage.value < totalPages.value);
  const hasPrevPage = computed(() => currentPage.value > 1);
  const isFirstPage = computed(() => currentPage.value === 1);
  const isLastPage = computed(() => currentPage.value === totalPages.value);

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };

  const nextPage = () => {
    if (hasNextPage.value) {
      currentPage.value++;
    }
  };

  const prevPage = () => {
    if (hasPrevPage.value) {
      currentPage.value--;
    }
  };

  const firstPage = () => {
    currentPage.value = 1;
  };

  const lastPage = () => {
    currentPage.value = totalPages.value;
  };

  const setTotalItems = (total) => {
    totalItems.value = total;
    // Adjust current page if it's beyond the new total pages
    if (currentPage.value > totalPages.value && totalPages.value > 0) {
      currentPage.value = totalPages.value;
    }
  };

  const reset = () => {
    currentPage.value = initialPage;
    totalItems.value = 0;
  };

  const paginateItems = (items) => {
    return items.slice(startIndex.value, endIndex.value);
  };

  return {
    currentPage,
    itemsPerPage,
    totalItems,
    totalPages,
    startIndex,
    endIndex,
    hasNextPage,
    hasPrevPage,
    isFirstPage,
    isLastPage,
    goToPage,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    setTotalItems,
    reset,
    paginateItems,
  };
}

/**
 * Tab management
 */
export function useTabs(initialTab = null, tabs = []) {
  const activeTab = ref(initialTab || (tabs.length > 0 ? tabs[0] : null));
  const tabHistory = ref([]);

  const setActiveTab = (tab) => {
    if (activeTab.value !== tab) {
      tabHistory.value.push(activeTab.value);
      activeTab.value = tab;
    }
  };

  const isActiveTab = (tab) => {
    return activeTab.value === tab;
  };

  const goToPreviousTab = () => {
    if (tabHistory.value.length > 0) {
      activeTab.value = tabHistory.value.pop();
    }
  };

  const resetTabs = () => {
    activeTab.value = initialTab || (tabs.length > 0 ? tabs[0] : null);
    tabHistory.value = [];
  };

  return {
    activeTab,
    tabHistory,
    setActiveTab,
    isActiveTab,
    goToPreviousTab,
    resetTabs,
  };
}

/**
 * Toggle state management
 */
export function useToggle(initialState = false) {
  const isToggled = ref(initialState);

  const toggle = () => {
    isToggled.value = !isToggled.value;
  };

  const setToggle = (state) => {
    isToggled.value = Boolean(state);
  };

  const enable = () => {
    isToggled.value = true;
  };

  const disable = () => {
    isToggled.value = false;
  };

  return {
    isToggled,
    toggle,
    setToggle,
    enable,
    disable,
  };
}

/**
 * Selection management (single and multiple)
 */
export function useSelection(options = {}) {
  const { multiple = false, initialSelection = multiple ? [] : null } = options;

  const selected = ref(initialSelection);
  const selectionHistory = ref([]);

  const select = (item) => {
    if (multiple) {
      if (!selected.value.includes(item)) {
        selected.value.push(item);
      }
    } else {
      if (selected.value !== item) {
        selectionHistory.value.push(selected.value);
        selected.value = item;
      }
    }
  };

  const deselect = (item) => {
    if (multiple) {
      const index = selected.value.indexOf(item);
      if (index > -1) {
        selected.value.splice(index, 1);
      }
    } else {
      if (selected.value === item) {
        selectionHistory.value.push(selected.value);
        selected.value = null;
      }
    }
  };

  const toggle = (item) => {
    if (isSelected(item)) {
      deselect(item);
    } else {
      select(item);
    }
  };

  const isSelected = (item) => {
    if (multiple) {
      return selected.value.includes(item);
    } else {
      return selected.value === item;
    }
  };

  const selectAll = (items) => {
    if (multiple) {
      selected.value = [...items];
    }
  };

  const deselectAll = () => {
    if (multiple) {
      selected.value = [];
    } else {
      selected.value = null;
    }
  };

  const getSelectedCount = () => {
    if (multiple) {
      return selected.value.length;
    } else {
      return selected.value ? 1 : 0;
    }
  };

  const hasSelection = computed(() => {
    if (multiple) {
      return selected.value.length > 0;
    } else {
      return selected.value !== null;
    }
  });

  return {
    selected,
    selectionHistory,
    select,
    deselect,
    toggle,
    isSelected,
    selectAll,
    deselectAll,
    getSelectedCount,
    hasSelection,
  };
}
