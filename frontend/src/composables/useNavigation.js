/**
 * Navigation Utilities Composable
 *
 * Provides reusable navigation patterns with:
 * - Router navigation helpers
 * - Navigation logging
 * - Route parameter handling
 * - Navigation guards
 * - Common navigation patterns
 */
import { useRouter, useRoute } from "vue-router";
import { logNavigation, logUserAction } from "@/utils/logger";

export function useNavigation() {
  const router = useRouter();
  const route = useRoute();

  /**
   * Navigate to a path with logging
   */
  const navigateTo = (path, options = {}) => {
    const {
      replace = false,
      logAction = true,
      actionName = "navigation",
      actionData = {},
    } = options;

    if (logAction) {
      logNavigation(`Navigating to: ${path}`, {
        from: route.path,
        to: path,
        ...actionData,
      });

      logUserAction(actionName, {
        path,
        from: route.path,
        ...actionData,
      });
    }

    if (replace) {
      return router.replace(path);
    } else {
      return router.push(path);
    }
  };

  /**
   * Navigate back with fallback
   */
  const goBack = (fallbackPath = "/") => {
    if (window.history.length > 1) {
      logNavigation("Navigating back");
      router.back();
    } else {
      navigateTo(fallbackPath, {
        logAction: true,
        actionName: "fallback_navigation",
        actionData: { reason: "no_history" },
      });
    }
  };

  /**
   * Navigate to home
   */
  const goHome = () => {
    navigateTo("/", {
      actionName: "go_home",
    });
  };

  /**
   * Navigate to skills list
   */
  const goToSkillsList = () => {
    navigateTo("/list-skills", {
      actionName: "view_skills_list",
    });
  };

  /**
   * Navigate to skill details
   */
  const goToSkillDetails = (skillId) => {
    navigateTo(`/skill-details/${skillId}`, {
      actionName: "view_skill_details",
      actionData: { skillId },
    });
  };

  /**
   * Navigate to create skill
   */
  const goToCreateSkill = () => {
    navigateTo("/create-skill", {
      actionName: "create_skill_form",
    });
  };

  /**
   * Navigate to assessments list
   */
  const goToAssessmentsList = () => {
    navigateTo("/list-assessments", {
      actionName: "view_assessments_list",
    });
  };

  /**
   * Navigate to assessment details
   */
  const goToAssessmentDetails = (assessmentId) => {
    navigateTo(`/assessment-details/${assessmentId}`, {
      actionName: "view_assessment_details",
      actionData: { assessmentId },
    });
  };

  /**
   * Navigate to create assessment
   */
  const goToCreateAssessment = () => {
    navigateTo("/create-assessment", {
      actionName: "create_assessment_form",
    });
  };

  /**
   * Navigate to sessions
   */
  const goToSessions = () => {
    navigateTo("/sessions", {
      actionName: "view_sessions",
    });
  };

  /**
   * Navigate to sessions list
   */
  const goToSessionsList = () => {
    navigateTo("/sessions-list", {
      actionName: "view_sessions_list",
    });
  };

  /**
   * Navigate to session details
   */
  const goToSessionDetails = (sessionId) => {
    navigateTo(`/session-details/${sessionId}`, {
      actionName: "view_session_details",
      actionData: { sessionId },
    });
  };

  /**
   * Navigate to take quiz
   */
  const goToTakeQuiz = (sessionCode = null) => {
    const path = sessionCode ? `/take-quiz/${sessionCode}` : "/take-quiz";
    navigateTo(path, {
      actionName: "start_quiz",
      actionData: { sessionCode },
    });
  };

  /**
   * Navigate to reports
   */
  const goToReports = () => {
    navigateTo("/reports", {
      actionName: "view_reports",
    });
  };

  /**
   * Navigate to user sessions
   */
  const goToUserSessions = () => {
    navigateTo("/user-sessions", {
      actionName: "view_user_sessions",
    });
  };

  /**
   * Navigate to link generation
   */
  const goToLinkGeneration = () => {
    navigateTo("/link", {
      actionName: "generate_link",
    });
  };

  /**
   * Navigate with confirmation
   */
  const navigateWithConfirmation = async (
    path,
    message = "Are you sure you want to leave this page?",
  ) => {
    const confirmed = window.confirm(message);
    if (confirmed) {
      navigateTo(path, {
        actionName: "confirmed_navigation",
        actionData: { confirmed: true },
      });
    } else {
      logUserAction("cancelled_navigation", {
        path,
        confirmed: false,
      });
    }
    return confirmed;
  };

  /**
   * Refresh current route
   */
  const refresh = () => {
    logNavigation("Refreshing current route", { path: route.path });
    router.go(0);
  };

  /**
   * Check if current route matches pattern
   */
  const isCurrentRoute = (pathPattern) => {
    if (typeof pathPattern === "string") {
      return route.path === pathPattern;
    }
    if (pathPattern instanceof RegExp) {
      return pathPattern.test(route.path);
    }
    return false;
  };

  /**
   * Get current route parameters
   */
  const getRouteParams = () => {
    return { ...route.params };
  };

  /**
   * Get current route query parameters
   */
  const getRouteQuery = () => {
    return { ...route.query };
  };

  /**
   * Update route query parameters
   */
  const updateQuery = (newQuery, options = {}) => {
    const { merge = true, replace = false } = options;

    const query = merge ? { ...route.query, ...newQuery } : newQuery;

    const navigationMethod = replace ? router.replace : router.push;
    return navigationMethod({
      path: route.path,
      query,
    });
  };

  return {
    // Router instances
    router,
    route,

    // Navigation methods
    navigateTo,
    goBack,
    goHome,

    // Specific navigation methods
    goToSkillsList,
    goToSkillDetails,
    goToCreateSkill,
    goToAssessmentsList,
    goToAssessmentDetails,
    goToCreateAssessment,
    goToSessions,
    goToSessionsList,
    goToSessionDetails,
    goToTakeQuiz,
    goToReports,
    goToUserSessions,
    goToLinkGeneration,

    // Utility methods
    navigateWithConfirmation,
    refresh,
    isCurrentRoute,
    getRouteParams,
    getRouteQuery,
    updateQuery,
  };
}
