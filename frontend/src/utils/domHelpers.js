/**
 * DOM Helper utilities to prevent "Node cannot be found" errors
 */

// Import logger inline to avoid circular dependencies
const logWarning = (message, data) => {
  import("./logger").then(({ warning }) => {
    warning(message, data);
  });
};

/**
 * Safely check if an element exists and has a specific method
 * @param {Element} element - Element to check
 * @param {string} methodName - Method name to check for
 * @returns {boolean} True if element exists and has the method
 */
export const hasMethod = (element, methodName) => {
  return (
    element &&
    typeof element === "object" &&
    typeof element[methodName] === "function"
  );
};

/**
 * Safely call a method on an element
 * @param {Element} element - Element to call method on
 * @param {string} methodName - Method name to call
 * @param {...any} args - Arguments to pass to the method
 * @returns {any} Method result or null if failed
 */
export const safeCallMethod = (element, methodName, ...args) => {
  try {
    if (hasMethod(element, methodName)) {
      return element[methodName](...args);
    }
    return null;
  } catch (error) {
    logWarning(`Safe call method failed for "${methodName}":`, {
      error: error.message,
    });
    return null;
  }
};

/**
 * Safely access a Vue ref value
 * @param {Ref} ref - Vue ref object
 * @returns {any} Ref value or null if invalid
 */
export const safeRefValue = (ref) => {
  try {
    return ref && typeof ref === "object" && "value" in ref ? ref.value : null;
  } catch (error) {
    logWarning("Safe ref value access failed:", { error: error.message });
    return null;
  }
};

/**
 * Safely call a method on a Vue ref value
 * @param {Ref} ref - Vue ref object
 * @param {string} methodName - Method name to call
 * @param {...any} args - Arguments to pass to the method
 * @returns {any} Method result or null if failed
 */
export const safeCallRefMethod = (ref, methodName, ...args) => {
  const value = safeRefValue(ref);
  return safeCallMethod(value, methodName, ...args);
};

/**
 * Safely copy text to clipboard with fallback
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export const safeCopyToClipboard = async (text) => {
  try {
    // Try modern clipboard API first
    if (
      navigator.clipboard &&
      typeof navigator.clipboard.writeText === "function"
    ) {
      await navigator.clipboard.writeText(text);
      return true;
    }

    // Fallback for older browsers
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand("copy");
    document.body.removeChild(textArea);
    return successful;
  } catch (error) {
    logWarning("Safe copy to clipboard failed:", { error: error.message });
    return false;
  }
};
