/**
 * Authentication and authorization helper functions
 */

import { debug, warning } from "./logger";
import config from "../config/globalConfig";

/**
 * Check if a user has admin access
 * This includes:
 * 1. Users in the 'admins' group
 * 2. Specific users from the 'employees' group who are granted admin access
 *
 * @param {Object} userInfo - User information object containing groups and user identifiers
 * @returns {boolean} - True if user has admin access, false otherwise
 */
export function hasAdminAccess(userInfo) {
  if (!userInfo) {
    debug("No user info provided for admin access check");
    return false;
  }

  // Get group names from environment variables
  const adminGroupName =
    import.meta.env.VITE_ADMIN_GROUP_NAME || config.defaultGroupName.admin;
  const employeeGroupName =
    import.meta.env.VITE_EMPLOYEE_GROUP_NAME ||
    config.defaultGroupName.employee;

  // Get list of employee users who should have admin access
  const employeeAdminUsersStr = import.meta.env.VITE_EMPLOYEE_ADMIN_USERS || "";
  const employeeAdminUsers = employeeAdminUsersStr
    .split(",")
    .map((user) => user.trim())
    .filter((user) => user.length > 0);

  debug("Admin access check configuration", {
    adminGroupName,
    employeeGroupName,
    employeeAdminUsers,
    userInfo: {
      sub: userInfo.sub,
      email: userInfo.email,
      groups: userInfo.groups,
    },
  });

  // Check if user has groups
  if (!userInfo.groups || !Array.isArray(userInfo.groups)) {
    warning("User has no groups array for admin access check");
    return false;
  }

  // Check if user is in admin group
  const hasAdminGroup = userInfo.groups.includes(adminGroupName);
  if (hasAdminGroup) {
    debug("User has admin group access");
    return true;
  }

  // Check if user is in employee group and is specifically granted admin access
  const hasEmployeeGroup = userInfo.groups.includes(employeeGroupName);
  if (hasEmployeeGroup && employeeAdminUsers.length > 0) {
    // Check against user ID (sub) and email
    const userId = userInfo.sub;
    const userEmail = userInfo.email;

    const isEmployeeAdmin = employeeAdminUsers.some((adminUser) => {
      return adminUser === userId || adminUser === userEmail;
    });

    if (isEmployeeAdmin) {
      debug("Employee user has specific admin access", {
        userId,
        userEmail,
        matchedAgainst: employeeAdminUsers,
      });
      return true;
    }
  }

  debug("User does not have admin access", {
    hasAdminGroup,
    hasEmployeeGroup,
    isInEmployeeAdminList: false,
  });

  return false;
}

/**
 * Check if a user should see the admin home interface
 * This is the same as hasAdminAccess for now, but separated for potential future logic
 *
 * @param {Object} userInfo - User information object
 * @returns {boolean} - True if user should see admin home
 */
export function shouldShowAdminHome(userInfo) {
  return hasAdminAccess(userInfo);
}

/**
 * Check if a user should see the user home interface
 * Users see user home if they have employee group but don't have admin access
 *
 * @param {Object} userInfo - User information object
 * @returns {boolean} - True if user should see user home
 */
export function shouldShowUserHome(userInfo) {
  if (!userInfo || !userInfo.groups || !Array.isArray(userInfo.groups)) {
    return false;
  }

  const employeeGroupName =
    import.meta.env.VITE_EMPLOYEE_GROUP_NAME ||
    config.defaultGroupName.employee;
  const hasEmployeeGroup = userInfo.groups.includes(employeeGroupName);
  const hasAdminAccess = shouldShowAdminHome(userInfo);

  // Show user home if user has employee group but not admin access
  return hasEmployeeGroup && !hasAdminAccess;
}

/**
 * Get user's primary role for display purposes
 *
 * @param {Object} userInfo - User information object
 * @returns {string} - User's primary role ('admin', 'employee', 'unknown')
 */
export function getUserPrimaryRole(userInfo) {
  if (hasAdminAccess(userInfo)) {
    return "admin";
  }

  if (!userInfo || !userInfo.groups || !Array.isArray(userInfo.groups)) {
    return "unknown";
  }

  const employeeGroupName =
    import.meta.env.VITE_EMPLOYEE_GROUP_NAME ||
    config.defaultGroupName.employee;
  if (userInfo.groups.includes(employeeGroupName)) {
    return "employee";
  }

  return "unknown";
}
