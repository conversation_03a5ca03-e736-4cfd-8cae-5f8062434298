/**
 * Simple test utility to verify fullscreen functionality
 */
import { debug } from "./logger"; // Adjust the path if necessary

export const testFullscreenSupport = () => {
  const element = document.documentElement;

  const hasFullscreenSupport = !!(
    element.requestFullscreen ||
    element.webkitRequestFullscreen ||
    element.msRequestFullscreen
  );

  const supportDetails = {
    standard: !!element.requestFullscreen,
    webkit: !!element.webkitRequestFullscreen,
    ms: !!element.msRequestFullscreen,
    overall: hasFullscreenSupport,
  };

  // Use the 'debug' logger with a clear message and a context object
  debug("Fullscreen API Support Check", supportDetails);

  return hasFullscreenSupport;
};

export const getCurrentFullscreenState = () => {
  const isFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  );

  // Use the 'debug' logger, passing the state as context
  debug("Current fullscreen state", { isFullscreen });

  return isFullscreen;
};
