<template>
  <PhantomLayout title="Reports">
    <div class="p-6 -mt-10">
      <!-- Tabs Container -->
      <div class="mb-8">
        <div
          class="flex space-x-1 bg-white/5 backdrop-blur-sm p-1 rounded-xl border border-white/10"
        >
          <button
            v-for="tab in tabs"
            :key="tab.value"
            class="flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200"
            :class="
              activeTab === tab.value
                ? `bg-gradient-to-r ${tab.gradient} text-white shadow-sm`
                : 'text-white/70 hover:text-white hover:bg-white/5'
            "
            @click="activeTab = tab.value"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <!-- Skillwise Report Tab Content -->
      <div v-if="activeTab === 'skill-wise'" class="card-phantom">
        <SkillwiseReport />
      </div>

      <!-- Assessment-wise Report Tab Content -->
      <div v-if="activeTab === 'assessment-wise'" class="card-phantom">
        <AssessmentWiseReport />
      </div>

      <!-- User-wise Report Tab Content -->
      <div v-if="activeTab === 'user-wise'" class="card-phantom">
        <UserWiseReport />
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTabs } from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";
import SkillwiseReport from "@/components/reports/SkillWiseReport.vue";
import AssessmentWiseReport from "@/components/reports/AssessmentWiseReport.vue";
import UserWiseReport from "@/components/reports/UserWiseReport.vue";

//----------------------------------------------------------------
// Dependencies & Configuration
//----------------------------------------------------------------
const route = useRoute();
const router = useRouter();

/**
 * Configuration for the report tabs. Storing this as an array of objects
 * makes the template clean and the component easy to modify.
 */
const tabs = [
  {
    label: "Skill-wise Report",
    value: "skill-wise",
    gradient: "from-phantom-blue to-phantom-indigo",
  },
  {
    label: "Assessment-wise Report",
    value: "assessment-wise",
    gradient: "from-phantom-indigo to-phantom-purple",
  },
  {
    label: "User-wise Report",
    value: "user-wise",
    gradient: "from-phantom-purple to-phantom-blue",
  },
];

// Tabs management using composable
const tabsManager = useTabs("skill-wise");
const activeTab = tabsManager.activeTab;

// Query parameter to tab value mapping
const queryToTabMap = {
  "skill-wise": "skill-wise",
  "assessment-wise": "assessment-wise",
  "user-wise": "user-wise",
};

/**
 * Watches for changes in the active tab and updates the URL query parameter.
 * This provides a better user experience by allowing for bookmarking and sharing
 * links directly to a specific tab. `router.replace` is used to avoid adding
 * every tab click to the browser's history.
 */
watch(activeTab, (newTabValue) => {
  const newQueryKey = Object.keys(queryToTabMap).find(
    (key) => queryToTabMap[key] === newTabValue,
  );

  if (newQueryKey && route.query.tab !== newQueryKey) {
    router.replace({ query: { tab: newQueryKey } });
  }
});

/**
 * On component mount, this function reads the URL query parameter to set the
 * initial active tab, allowing users to land on a specific report type directly.
 */
onMounted(() => {
  const tabQuery = route.query.tab;
  if (tabQuery && queryToTabMap[tabQuery]) {
    activeTab.value = queryToTabMap[tabQuery];
  }
});
</script>
