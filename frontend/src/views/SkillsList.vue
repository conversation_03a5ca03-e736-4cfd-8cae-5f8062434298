<template>
  <PhantomLayout title="Skills" :no-scroll="true">
    <div class="p-6 -mt-10">
      <!-- Removed the top action bar as we'll move the button next to search -->

      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
        />
        <span class="ml-3 text-white/80">Loading skills...</span>
      </div>

      <!-- Error message -->
      <div
        v-if="message && !isSuccess"
        class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-red-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div
        v-if="message && isSuccess"
        class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-green-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Search bar with Add Skill button - Always visible -->
      <div
        class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6"
      >
        <div class="w-full flex flex-row items-center">
          <div class="relative flex-1 mr-3">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search skills..."
              class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
          <!-- Add Skill button moved next to search bar -->
          <button
            class="btn-phantom px-5 py-2.5 text-sm whitespace-nowrap"
            @click="navigateTo('/create-skill')"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add Skill
            </span>
          </button>
        </div>
      </div>

      <!-- No skills message -->
      <div
        v-if="!isLoading && !message && skills.length === 0"
        class="text-center py-16"
      >
        <div
          class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-10 w-10 text-white/40"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No skills found</h3>
        <p class="text-white/60 mb-6">
          Get started by creating your first skill category
        </p>
      </div>

      <!-- Skills table -->
      <div v-if="!isLoading && skills.length > 0">
        <!-- Skills table -->
        <div class="rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Name</th>
                <th class="py-4 px-6 text-white/80 font-medium">Description</th>
                <th class="py-4 px-6 text-white/80 font-medium">Questions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(skill, index) in paginatedSkills"
                :key="skill.id"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                @click="navigateToSkillDetail(skill)"
              >
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <div
                      class="w-10 h-10 rounded-full bg-gradient-to-br from-phantom-blue to-phantom-indigo flex items-center justify-center mr-3"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                        />
                      </svg>
                    </div>
                    <span class="font-medium text-white">{{ skill.name }}</span>
                  </div>
                </td>
                <td class="py-4 px-6 text-white/70">
                  <div class="max-w-xs truncate">
                    {{ getCleanDescription(skill.description) }}
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-phantom-blue mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span class="text-white/80">{{
                      skill.question_count || 0
                    }}</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination Controls -->
          <div v-if="displayTotalPages > 1" class="mt-3">
            <Pagination
              :current-page="currentPage"
              :total-pages="displayTotalPages"
              :total-items="searchQuery ? filteredSkills.length : totalItems"
              :items-per-page="itemsPerPage"
              @page-change="onPageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractPaginationMeta,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { getSkillHashId } from "@/utils/hashIds";
import { useNavigation } from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";
import { Pagination } from "@/components/ui/pagination";

//----------------------------------------------------------------
// Dependencies & Navigation
//----------------------------------------------------------------
const router = useRouter();
const { navigateTo } = useNavigation();
const navigateToSkillDetail = (skill) => {
  const hashId = getSkillHashId(skill);
  router.push(`/skill/${hashId}`);
};

const { message, isSuccess, setErrorMessage, clearMessage } =
  useMessageHandler();

//----------------------------------------------------------------
// Component State
//----------------------------------------------------------------
const isLoading = ref(true);
const searchQuery = ref("");
const isSearching = ref(false);

// `skills` holds the current page of data fetched from the server.
const skills = ref([]);
// `allSkills` is a cache of all records, fetched once for client-side searching.
const allSkills = ref([]);

// State for server-side pagination.
const currentPage = ref(1);
const itemsPerPage = 7;
const totalItems = ref(0);
const totalPages = ref(0);

//----------------------------------------------------------------
// Hybrid Search & Pagination Logic
//----------------------------------------------------------------
// This component uses a hybrid approach:
// 1. **Default Mode**: Uses server-side pagination (`fetchSkills`) for efficiency.
// 2. **Search Mode**: Fetches all skills once (`fetchAllSkills`), then performs
//    filtering and pagination on the client for a fast, responsive search experience.

/**
 * Filters skills based on the search query. This computed property is central
 * to the hybrid strategy. It uses `allSkills` for searching and the paginated
 * `skills` list otherwise.
 */
const filteredSkills = computed(() => {
  if (!isSearching.value) {
    return skills.value; // Not searching, so return the server-paginated list.
  }

  const query = searchQuery.value.toLowerCase().trim();
  // Use the comprehensive `allSkills` list for client-side filtering.
  return allSkills.value.filter((skill) => {
    if (!skill) return false;
    const nameMatch = skill.name && skill.name.toLowerCase().includes(query);
    const descriptionMatch =
      skill.description && skill.description.toLowerCase().includes(query);
    return nameMatch || descriptionMatch;
  });
});

/**
 * Returns the final list of skills to be displayed on the current page.
 * - If not searching, it's simply the data from the server (`skills`).
 * - If searching, it applies client-side pagination to the `filteredSkills`.
 */
const paginatedSkills = computed(() => {
  if (!isSearching.value) {
    return skills.value;
  }
  // Apply pagination to the client-side filtered results.
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredSkills.value.slice(startIndex, endIndex);
});

/**
 * Determines the total number of pages to display in the pagination component.
 * It dynamically switches between server-provided total pages and a client-calculated value.
 */
const displayTotalPages = computed(() => {
  if (!isSearching.value) {
    return totalPages.value; // Use server-side total pages.
  }
  // Calculate total pages based on the client-side filtered results.
  return Math.ceil(filteredSkills.value.length / itemsPerPage);
});

/**
 * Handles page changes from the pagination component.
 * It only triggers a new API call if we are NOT in search mode.
 */
const onPageChange = (page) => {
  currentPage.value = page;
  if (!isSearching.value) {
    fetchSkills();
  }
};

/**
 * Cleans and truncates a skill description, removing a common prefix.
 */
const getCleanDescription = (description) => {
  if (!description) return "No description available";
  const prefix = "### **Topic Description**";
  const cleanText = description.trim().startsWith(prefix)
    ? description.slice(prefix.length)
    : description;
  return cleanText.replace(/(\r\n|\n|\r)/gm, " ").trim();
};

//----------------------------------------------------------------
// API Calls
//----------------------------------------------------------------

/**
 * Fetches a single page of skills from the API using server-side pagination.
 */
const fetchSkills = async () => {
  isLoading.value = true;
  clearMessage();
  try {
    const offset = (currentPage.value - 1) * itemsPerPage;
    const response = await api.admin.getSkills({ limit: itemsPerPage, offset });

    skills.value = extractResponseData(response) || [];
    const paginationMeta = extractPaginationMeta(response);

    if (paginationMeta) {
      totalItems.value = paginationMeta.total;
      totalPages.value = Math.ceil(paginationMeta.total / itemsPerPage);
    }
  } catch (error) {
    logError(error, "fetchSkills");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || "Failed to fetch skills.");
  } finally {
    isLoading.value = false;
  }
};

/**
 * Fetches all skills from the paginated API endpoint by making sequential requests.
 * This populates the `allSkills` ref, which is used for client-side searching.
 */
const fetchAllSkills = async () => {
  try {
    let allData = [];
    let offset = 0;
    const limit = 100; // Fetch in batches of 100 for efficiency.
    let hasMore = true;

    while (hasMore) {
      const response = await api.admin.getSkills({ limit, offset });
      const skillsData = extractResponseData(response);

      if (skillsData && skillsData.length > 0) {
        allData = allData.concat(skillsData);
        const paginationMeta = extractPaginationMeta(response);
        hasMore = paginationMeta
          ? offset + limit < paginationMeta.total
          : skillsData.length === limit;
        offset += limit;
      } else {
        hasMore = false;
      }
    }
    allSkills.value = allData;
  } catch (error) {
    logError(error, "fetchAllSkills");
    // Even if this fails, the app can fall back to searching the current page's data.
    setErrorMessage("Could not fetch full skill list for searching.");
  }
};

//----------------------------------------------------------------
// Watchers & Lifecycle Hooks
//----------------------------------------------------------------

/**
 * This watcher is the main controller for the hybrid pagination/search logic.
 * It switches the component's behavior when the user types in the search box.
 */
watch(searchQuery, async (newQuery) => {
  currentPage.value = 1; // Always reset to the first page on a new search.

  if (newQuery && newQuery.trim() !== "") {
    isSearching.value = true;
    // If the full skill list hasn't been fetched yet, fetch it now.
    if (allSkills.value.length === 0) {
      await fetchAllSkills();
    }
  } else {
    // When the search query is cleared, switch back to server-side pagination.
    isSearching.value = false;
    await fetchSkills();
  }
});

onMounted(fetchSkills);
</script>
