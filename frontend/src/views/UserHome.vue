<template>
  <div class="h-screen overflow-hidden">
    <!-- Main Content -->
    <main class="h-full overflow-hidden">
      <!-- Hero Section with HERBIT ASCII Art -->
      <PhantomHero />
    </main>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import PhantomHero from "../components/layout/Hero.vue";

// Disable page scrolling when component mounts
onMounted(() => {
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";
});

// Re-enable page scrolling when component unmounts
onUnmounted(() => {
  document.body.style.overflow = "";
  document.documentElement.style.overflow = "";
});
</script>
