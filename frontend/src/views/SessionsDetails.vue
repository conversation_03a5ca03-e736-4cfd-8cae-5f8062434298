<template>
  <PhantomLayout title="Session Details">
    <div class="p-6">
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
        />
        <span class="ml-3 text-white/80">Loading session details...</span>
      </div>

      <!-- Error message -->
      <div
        v-if="message && !isSuccess"
        class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-red-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Session details card -->
      <div v-if="!isLoading && session" class="card-phantom">
        <div class="p-6">
          <h2 class="text-xl font-semibold text-white mb-6 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2 text-phantom-blue"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Session Information
          </h2>

          <!-- Renders all session details using a loop for maintainability -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div
              v-for="detail in sessionDetailsForDisplay"
              :key="detail.label"
              class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10"
            >
              <p class="text-white/60 text-sm mb-1">
                {{ detail.label }}
              </p>

              <!-- Conditional rendering for special display types -->
              <span
                v-if="detail.type === 'badge'"
                class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                :class="detail.class"
              >
                {{ detail.value }}
              </span>
              <p
                v-else-if="detail.type === 'code'"
                class="font-mono bg-white/10 text-phantom-blue px-3 py-1 rounded-lg inline-block"
              >
                {{ detail.value }}
              </p>
              <p v-else class="text-white font-medium">
                {{ detail.value }}
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end mt-6">
            <button
              class="btn-phantom-secondary px-5 py-2.5"
              @click="navigateBack"
            >
              <span class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
                Back to Sessions
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { extractResponseData } from "@/utils/apiResponseHandler";
import {
  isHashId,
  decodeSessionId,
  decodeSessionCodeFromHash,
} from "@/utils/hashIds";
import { warning } from "@/utils/logger";
import PhantomLayout from "@/components/layout/Layout.vue";

//----------------------------------------------------------------
// Dependencies & Services
//----------------------------------------------------------------
const route = useRoute();
const router = useRouter();
const { message, isSuccess, setErrorMessage, clearMessage } =
  useMessageHandler();

//----------------------------------------------------------------
// Component State
//----------------------------------------------------------------
const sessionIdFromUrl = route.params.sessionId;
const session = ref(null);
const isLoading = ref(false);

//----------------------------------------------------------------
// Computed Properties
//----------------------------------------------------------------

/**
 * Defensively gets the session code for display from multiple possible fields
 * to handle variations in API responses.
 */
const displaySessionCode = computed(() => {
  if (!session.value) return "N/A";
  return (
    session.value.session_code ||
    session.value.code ||
    session.value.sessionCode ||
    "N/A"
  );
});

/**
 * This computed property structures the session data into a "view model" array.
 * This simplifies the template by allowing a v-for loop to render the details,
 * making it easier to add, remove, or reorder fields in the future.
 */
const sessionDetailsForDisplay = computed(() => {
  if (!session.value) return [];

  const details = [
    { label: "Username", value: session.value.username },
    { label: "Session Code", value: displaySessionCode.value, type: "code" },
    { label: "Assessment", value: session.value.assessment_name },
  ];

  if (session.value.assessment_id_hash || session.value.assessment_id) {
    details.push({
      label: "Assessment ID",
      value: session.value.assessment_id_hash || session.value.assessment_id,
      type: "mono", // Can be used for mono font styling if needed
    });
  }

  details.push({
    label: "Status",
    value: session.value.status || "Pending",
    type: "badge",
    class: getStatusClass(session.value.status),
  });

  if (session.value.created_at) {
    details.push({
      label: "Created At",
      value: formatDate(session.value.created_at),
    });
  }

  if (session.value.completed_at) {
    details.push({
      label: "Completed At",
      value: formatDate(session.value.completed_at),
    });
  }

  return details;
});

//----------------------------------------------------------------
// UI Helpers & Navigation
//----------------------------------------------------------------

const navigateBack = () => router.push("/sessions");

const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString() + " " + date.toLocaleTimeString();
};

const getStatusClass = (status) => {
  switch (status) {
    case "completed":
    case "finished":
      return "bg-green-500/10 text-green-400 border border-green-500/20";
    case "in_progress":
    case "started":
      return "bg-blue-500/10 text-blue-400 border border-blue-500/20";
    case "pending":
    case "created":
    default:
      return "bg-yellow-500/10 text-yellow-400 border border-yellow-500/20";
  }
};

//----------------------------------------------------------------
// API & Data Fetching
//----------------------------------------------------------------

const fetchSessionDetails = async () => {
  isLoading.value = true;
  clearMessage();

  try {
    let apiIdentifier = sessionIdFromUrl;

    /**
     * The session identifier from the URL could be a hash, a numeric ID, or a 6-digit code.
     * This logic attempts to decode it in order of preference (numeric ID > 6-digit code)
     * to find the most reliable identifier for the API call. If decoding fails, it
     * falls back to using the original identifier from the URL.
     */
    if (isHashId(sessionIdFromUrl)) {
      try {
        const decodedId = await decodeSessionId(sessionIdFromUrl);
        if (decodedId) {
          apiIdentifier = decodedId.toString();
        } else {
          const decodedCode = await decodeSessionCodeFromHash(sessionIdFromUrl);
          if (decodedCode) {
            apiIdentifier = decodedCode;
          }
        }
      } catch (decodeError) {
        warning("Error decoding session identifier, using original value.", {
          decodeError,
        });
      }
    }

    const response = await api.admin.getSessionDetails(apiIdentifier);
    const responseData = extractResponseData(response);

    if (responseData) {
      session.value = responseData;

      // Ensure a displayable session code exists, even if the primary `session_code` field is null.
      if (!session.value.session_code && !session.value.code) {
        if (/^\d{6}$/.test(sessionIdFromUrl)) {
          session.value.session_code = sessionIdFromUrl;
        } else if (isHashId(sessionIdFromUrl)) {
          session.value.session_code =
            await decodeSessionCodeFromHash(sessionIdFromUrl);
        }
      }
    } else {
      setErrorMessage(`Session with ID ${sessionIdFromUrl} not found.`);
    }
  } catch (err) {
    logError(err, "fetchSessionDetails");
    setErrorMessage(getErrorMessage(err, "Failed to fetch session details."));
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  if (sessionIdFromUrl) {
    fetchSessionDetails();
  } else {
    setErrorMessage("No session ID was provided in the URL.");
  }
});
</script>
