<template>
  <div class="app-container" :class="{ 'no-scroll': isNoScrollPage }">
    <!-- Phantom-inspired background elements -->
    <div class="fixed inset-0 z-0 overflow-hidden">
      <!-- Gradient background similar to Phantom -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-[#0A0E17] via-[#0F1623] to-[#131B2E] animate-bg-shift"
      />

      <!-- Glowing orbs similar to Phantom -->
      <div
        v-for="i in 5"
        :key="`orb-${i}`"
        class="absolute rounded-full animate-glow-pulse"
        :class="[
          i % 5 === 0 ? 'bg-blue-500/20' : '',
          i % 5 === 1 ? 'bg-indigo-500/20' : '',
          i % 5 === 2 ? 'bg-purple-500/20' : '',
          i % 5 === 3 ? 'bg-cyan-500/20' : '',
          i % 5 === 4 ? 'bg-violet-500/20' : '',
        ]"
        :style="{
          width: `${Math.random() * 500 + 200}px`,
          height: `${Math.random() * 500 + 200}px`,
          top: `${Math.random() * 100}%`,
          left: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 5}s`,
          opacity: 0.4,
        }"
      />

      <!-- Subtle grid overlay -->
      <div class="absolute inset-0 bg-grid-pattern opacity-10" />
    </div>

    <!-- Fixed Header - Only show on non-auth pages and non-quiz pages -->
    <PhantomHeader v-if="!isAuthPage && !isTakeQuizPage" />

    <!-- Main content with conditional padding based on whether header/footer are shown -->
    <div
      class="relative z-10 min-h-screen"
      :class="{
        'pt-20 ': !isAuthPage && !isTakeQuizPage,
        'no-scroll': isNoScrollPage,
        'auth-page': isAuthPage,
      }"
    >
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" :key="$route.fullPath" />
        </transition>
      </router-view>
    </div>

    <!-- Fixed Footer - Only show on non-auth pages and non-quiz pages
    <PhantomFooter v-if="!isAuthPage && !isTakeQuizPage" /> -->
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { computed, watch, onMounted, onUnmounted } from "vue";
import PhantomHeader from "./components/layout/Header.vue";
// import PhantomFooter from "./components/layout/Footer.vue";

const route = useRoute();

// Determine if current route is an auth page (login, callback, etc.)
const isAuthPage = computed(() => {
  const authPages = ["/login", "/callback"];
  return authPages.includes(route.path);
});

// Determine if current route is the take quiz page
const isTakeQuizPage = computed(() => {
  return route.path.includes("/quiz/") || route.path.includes("/take-quiz");
});

// Determine if current route should have scrolling disabled
const isNoScrollPage = computed(() => {
  if (route.path === "/") {
    return true;
  }

  const noScrollPages = [
    "/create-skill",
    "/list-skills",
    "/list-assessments",
    "/sessions",
  ];
  return noScrollPages.some((page) => route.path.includes(page));
});

// Update body class when isNoScrollPage changes
watch(
  isNoScrollPage,
  (newValue) => {
    if (newValue) {
      document.body.classList.add("no-scroll-active");
    } else {
      document.body.classList.remove("no-scroll-active");
    }
  },
  { immediate: true },
);

// Set initial body class on mount
onMounted(() => {
  if (isNoScrollPage.value) {
    document.body.classList.add("no-scroll-active");
  }
});

// Clean up on unmount
onUnmounted(() => {
  document.body.classList.remove("no-scroll-active");
});
</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  font-family:
    "Inter",
    "SF Pro Display",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
}

/* Apply no-scroll to body when needed */
body.no-scroll-active {
  overflow: hidden !important;
}

.app-container {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

/* Main content area with responsive padding - only for non-auth pages */
.app-container > div.relative.z-10:not(.auth-page) {
  flex: 1;
  padding-top: 4rem; /* For header space */
  padding-bottom: 3.5rem; /* For footer space */
}

@media (max-width: 768px) {
  .app-container > div.relative.z-10:not(.auth-page) {
    padding-top: 3.5rem;
    padding-bottom: 3rem;
  }
}

/* Phantom-inspired transitions */
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Phantom-inspired grid pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Global animation classes */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out forwards;
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out forwards;
}

/* Phantom-inspired button styles */
.btn-phantom {
  @apply relative overflow-hidden rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 px-5 py-2.5 text-white font-medium transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 focus:outline-none focus:ring-2 focus:ring-blue-500/50;
}

.btn-phantom::before {
  content: "";
  @apply absolute inset-0 bg-gradient-to-r from-indigo-600 to-blue-500 opacity-0 transition-opacity duration-300;
}

.btn-phantom:hover::before {
  @apply opacity-100;
}

.btn-phantom-secondary {
  @apply relative overflow-hidden rounded-full bg-white/10 backdrop-blur-sm px-5 py-2.5 text-white font-medium border border-white/20 transition-all duration-300 hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30;
}

/* Phantom-inspired card styles - removed styling */
.card-phantom {
  @apply p-6 transition-all duration-300;
}

/* No scroll class for specific pages */
.no-scroll {
  overflow: hidden !important;
  height: 100vh !important;
}
</style>
