/* ======= CONSOLIDATED THEME STYLES ======= */

/* ======= PHANTOM THEME COMPONENTS ======= */

/* Global styles to ensure button text remains visible on hover */
button span,
a span,
.btn-phantom span,
.btn-phantom-secondary span,
button:hover span,
a:hover span,
.btn-phantom:hover span,
.btn-phantom-secondary:hover span,
.skill-gradient-button span,
.skill-back-button span,
.submit-quiz-button span,
.skill-gradient-button:hover span,
.skill-back-button:hover span,
router-link span,
router-link:hover span,
.submit-quiz-button:hover span {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
  position: relative;
  z-index: 10;
}

/* Ensure all button text is visible */
[class*="btn-"] span,
[class*="button"] span,
button span,
a[class*="btn"] span {
  opacity: 1 !important;
  visibility: visible !important;
  position: relative;
  z-index: 10;
}

/* Phantom theme animations */
@keyframes float-slow {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
}

@keyframes float-slow-reverse {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(20px) translateX(-10px);
  }
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

@keyframes ping-slow {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-slow-reverse {
  animation: float-slow-reverse 9s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 10s ease-in-out infinite;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Phantom theme utilities */
.bg-grid-phantom {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

.bg-radial-vignette {
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

/* Phantom theme components */
.card-phantom {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl;
}

.btn-phantom {
  @apply bg-gradient-to-r from-phantom-blue to-phantom-indigo text-white font-medium rounded-lg
         shadow-lg shadow-phantom-blue/20 hover:shadow-phantom-blue/30
         transition-all duration-200 flex items-center justify-center
         focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 disabled:opacity-50
         hover:text-white; /* Added to ensure text remains visible on hover */
  position: relative;
  color: white !important;
}

.btn-phantom::after {
  content: "";
  position: absolute;
  inset: 0;
  background: transparent;
  z-index: 1;
}

.btn-phantom > * {
  position: relative;
  z-index: 2;
}

.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg
         hover:bg-white/10 transition-all duration-200 flex items-center justify-center
         focus:outline-none focus:ring-2 focus:ring-white/20 disabled:opacity-50
         hover:text-white; /* Added to ensure text remains visible on hover */
  position: relative;
  color: white !important;
}

.btn-phantom-secondary::after {
  content: "";
  position: absolute;
  inset: 0;
  background: transparent;
  z-index: 1;
}

.btn-phantom-secondary > * {
  position: relative;
  z-index: 2;
}

/* Underline gradient effect */
.underline-gradient {
  position: relative;
}

.underline-gradient::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #3898ff, #7b5ffd);
  border-radius: 2px;
}

/* Custom scrollbar for Phantom theme */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* ======= HERBIT ASCII Art Animations ======= */

/* Typewriter Effect */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-cursor {
  from,
  to {
    border-color: transparent;
  }
  50% {
    border-color: #00ffff;
  }
}

.herbit-typewriter {
  overflow: hidden;
  border-right: 2px solid #00ffff;
  white-space: nowrap;
  animation:
    typewriter 4s steps(40, end),
    blink-cursor 0.75s step-end infinite;
}

/* Glitch Effect */
@keyframes glitch-1 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitch-2 {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(2px, 0px);
  }
  40% {
    transform: translate(-2px, 0px);
  }
  60% {
    transform: translate(0px, 2px);
  }
  80% {
    transform: translate(0px, -2px);
  }
}

@keyframes glitch-color {
  0%,
  100% {
    text-shadow:
      0.05em 0 0 #00ffff,
      -0.05em -0.025em 0 #ff00ff,
      0.025em 0.05em 0 #ffff00;
  }
  15% {
    text-shadow:
      0.05em 0 0 #00ffff,
      -0.05em -0.025em 0 #ff00ff,
      0.025em 0.05em 0 #ffff00;
  }
  16% {
    text-shadow:
      -0.05em -0.025em 0 #00ffff,
      0.025em 0.025em 0 #ff00ff,
      -0.05em -0.05em 0 #ffff00;
  }
  49% {
    text-shadow:
      -0.05em -0.025em 0 #00ffff,
      0.025em 0.025em 0 #ff00ff,
      -0.05em -0.05em 0 #ffff00;
  }
  50% {
    text-shadow:
      0.025em 0.05em 0 #00ffff,
      0.05em 0 0 #ff00ff,
      0 -0.05em 0 #ffff00;
  }
  99% {
    text-shadow:
      0.025em 0.05em 0 #00ffff,
      0.05em 0 0 #ff00ff,
      0 -0.05em 0 #ffff00;
  }
}

.herbit-glitch {
  position: relative;
  animation: glitch-color 2s infinite;
}

.herbit-glitch::before,
.herbit-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.herbit-glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff00ff;
  z-index: -1;
}

.herbit-glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ffff;
  z-index: -2;
}

/* Wave Reveal Effect */
@keyframes wave-reveal {
  0% {
    opacity: 0;
    transform: translateY(20px) rotateX(90deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
  }
}

.herbit-wave {
  perspective: 1000px;
}

.herbit-wave .char {
  display: inline-block;
  opacity: 0;
  animation: wave-reveal 0.6s ease-out forwards;
}

/* Matrix Style Cascade */
@keyframes matrix-cascade {
  0% {
    opacity: 0;
    transform: translateY(-50px);
    color: #00ff00;
  }
  50% {
    color: #00ff00;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    color: #00ffff;
  }
}

.herbit-matrix .char {
  display: inline-block;
  opacity: 0;
  animation: matrix-cascade 0.8s ease-out forwards;
}

/* Neon Glow Effect */
@keyframes neon-flicker {
  0%,
  100% {
    text-shadow:
      0 0 5px #00ffff,
      0 0 10px #00ffff,
      0 0 15px #00ffff,
      0 0 20px #00ffff;
  }
  50% {
    text-shadow:
      0 0 2px #00ffff,
      0 0 5px #00ffff,
      0 0 8px #00ffff,
      0 0 12px #00ffff;
  }
}

.herbit-neon {
  color: #00ffff;
  animation: neon-flicker 2s ease-in-out infinite alternate;
}

/* Hologram Effect */
@keyframes hologram {
  0%,
  100% {
    opacity: 1;
    text-shadow:
      0 0 5px rgba(0, 255, 255, 0.8),
      0 0 10px rgba(0, 255, 255, 0.6),
      0 0 15px rgba(0, 255, 255, 0.4);
  }
  25% {
    opacity: 0.8;
    transform: translateX(1px);
  }
  50% {
    opacity: 0.9;
    text-shadow:
      0 0 3px rgba(0, 255, 255, 0.9),
      0 0 8px rgba(0, 255, 255, 0.7),
      0 0 12px rgba(0, 255, 255, 0.5);
  }
  75% {
    opacity: 0.7;
    transform: translateX(-1px);
  }
}

.herbit-hologram {
  animation: hologram 3s ease-in-out infinite;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation:
    hologram 3s ease-in-out infinite,
    shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Advanced Typewriter with Character Reveal */
@keyframes char-reveal {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.herbit-char-typewriter .char {
  display: inline-block;
  opacity: 0;
  animation: char-reveal 0.1s ease-out forwards;
}

/* Scanning Line Effect */
@keyframes scan-line {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.herbit-scan {
  position: relative;
  overflow: hidden;
}

.herbit-scan::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  animation: scan-line 3s ease-in-out infinite;
  z-index: 10;
}

/* Terminal Boot Effect */
@keyframes terminal-boot {
  0% {
    opacity: 0;
    filter: blur(2px);
  }
  20% {
    opacity: 0.3;
    filter: blur(1px);
  }
  40% {
    opacity: 0.6;
    filter: blur(0.5px);
  }
  60% {
    opacity: 0.8;
    filter: blur(0.2px);
  }
  100% {
    opacity: 1;
    filter: blur(0);
  }
}

.herbit-terminal-boot {
  animation: terminal-boot 2s ease-out forwards;
  opacity: 0;
}

/* Cyberpunk Flicker */
@keyframes cyberpunk-flicker {
  0%,
  100% {
    opacity: 1;
    text-shadow:
      0 0 5px #00ffff,
      0 0 10px #00ffff,
      0 0 15px #00ffff;
  }
  2% {
    opacity: 0.8;
    text-shadow:
      0 0 3px #00ffff,
      0 0 6px #00ffff,
      0 0 9px #00ffff;
  }
  4% {
    opacity: 1;
  }
  8% {
    opacity: 0.9;
  }
  10% {
    opacity: 1;
  }
  12% {
    opacity: 0.7;
  }
  14% {
    opacity: 1;
  }
  16% {
    opacity: 0.95;
  }
  18% {
    opacity: 1;
  }
  22% {
    opacity: 0.85;
  }
  24% {
    opacity: 1;
  }
}

.herbit-cyberpunk {
  animation: cyberpunk-flicker 4s ease-in-out infinite;
}

/* ======= LAYOUT STYLES ======= */

/* Page background styles */
.herbit-page-bg {
  min-height: 100vh;
  background-color: #1e1e1e;
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
}

.herbit-page-bg-gradient {
  min-height: 100vh;
  background-image: linear-gradient(
    to bottom right,
    rgb(3, 7, 18),
    rgb(17, 24, 39),
    rgb(0, 0, 0)
  );
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif;
  position: relative;
  overflow: hidden;
}

/* Advanced background with particles and effects */
.herbit-bg-advanced {
  height: 100%;
  width: 100%;
  background-image: linear-gradient(
    to bottom right,
    rgb(3, 7, 18),
    rgb(17, 24, 39),
    rgb(0, 0, 0)
  );
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif;
  position: fixed;
  overflow: hidden;
  z-index: 0;
}

/* Particle element for advanced background */
.herbit-particle {
  position: absolute;
  border-radius: 50%;
  animation: float-particle 30s linear infinite;
  filter: blur(0.5px);
}

.herbit-particle-cyan {
  background-color: rgba(103, 232, 249, 0.3);
  box-shadow: 0 0 8px rgba(103, 232, 249, 0.6);
}

.herbit-particle-purple {
  background-color: rgba(168, 85, 247, 0.3);
  box-shadow: 0 0 8px rgba(168, 85, 247, 0.6);
}

.herbit-particle-blue {
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.herbit-particle-indigo {
  background-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.6);
}

.herbit-particle-teal {
  background-color: rgba(45, 212, 191, 0.3);
  box-shadow: 0 0 8px rgba(45, 212, 191, 0.6);
}

/* Glowing orb for advanced background */
.herbit-glow-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(3rem);
  animation: pulse-glow 15s ease-in-out infinite alternate;
}

.herbit-glow-orb-cyan {
  background-color: rgba(8, 145, 178, 0.15);
}

.herbit-glow-orb-purple {
  background-color: rgba(147, 51, 234, 0.15);
}

.herbit-glow-orb-blue {
  background-color: rgba(37, 99, 235, 0.15);
}

.herbit-glow-orb-indigo {
  background-color: rgba(79, 70, 229, 0.15);
}

.herbit-glow-orb-teal {
  background-color: rgba(13, 148, 136, 0.15);
}

/* Container styles */
.herbit-container {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  background-color: rgb(31, 41, 55);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 0.5rem;
  padding: 2rem;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  position: relative;
  z-index: 1;
}

.herbit-container-sm {
  width: 100%;
  max-width: 42rem;
  background-color: rgb(31, 41, 55);
  color: rgb(209, 213, 219);
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  position: relative;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  z-index: 1;
}

.herbit-container-md {
  width: 100%;
  max-width: 56rem;
  background-color: rgb(31, 41, 55);
  color: rgb(209, 213, 219);
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  position: relative;
  border-width: 2px;
  border-style: dashed;
  border-color: rgb(103, 232, 249);
  z-index: 1;
}

/* Glass container styles */
.herbit-glass-container {
  position: relative;
  backdrop-filter: blur(12px);
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateZ(0);
  transition: transform 0.5s ease;
  z-index: 1;
}

.herbit-glass-container:hover {
  transform: scale(1.01);
}

.herbit-glass-container-sm {
  position: relative;
  backdrop-filter: blur(12px);
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateZ(0);
  transition: transform 0.5s ease;
  z-index: 1;
  max-width: 42rem;
  width: 100%;
}

/* Button styles */
.herbit-btn-primary {
  background-color: rgb(8, 145, 178);
  color: white;
  transition: background-color 0.2s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-primary:hover {
  background-color: rgb(14, 116, 144);
}

.herbit-btn-secondary {
  background-color: rgba(31, 41, 55, 0.7);
  color: rgb(103, 232, 249);
  border: 1px solid rgba(103, 232, 249, 0.5);
  transition: all 0.2s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.herbit-btn-secondary:hover {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: rgb(103, 232, 249);
}

/* Card styles */
.herbit-card-highlight {
  position: relative;
  padding: 1.25rem;
  border-radius: 0.75rem;
  background-color: rgba(31, 41, 55, 0.7);
  border: 1px solid rgba(103, 232, 249, 0.3);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.herbit-card-highlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(103, 232, 249, 0.7),
    rgba(34, 211, 238, 0.3),
    rgba(103, 232, 249, 0.7)
  );
}

.herbit-glass-card {
  backdrop-filter: blur(12px);
  background-color: rgba(31, 41, 55, 0.5);
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* ======= MARKDOWN CONTENT STYLING ======= */

/* Markdown content styling */
.markdown-content h1 {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700; /* font-bold */
  color: #9d5fff; /* text-phantom-purple */
  margin-bottom: 1rem; /* mb-4 */
}

.markdown-content h2 {
  font-size: 1.25rem; /* text-xl */
  font-weight: 700; /* font-bold */
  color: #7b5ffd; /* text-phantom-indigo */
  margin-bottom: 0.75rem; /* mb-3 */
}

.markdown-content h3 {
  font-size: 1.125rem; /* text-lg */
  font-weight: 500; /* font-medium */
  color: #3898ff; /* text-phantom-blue */
  margin-bottom: 0.5rem; /* mb-2 */
}

.markdown-content p {
  margin-bottom: 1rem; /* mb-4 */
  color: rgba(255, 255, 255, 0.9); /* text-white/90 */
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.25rem; /* pl-5 */
  margin-bottom: 1rem; /* mb-4 */
}

.markdown-content li {
  margin-bottom: 0.25rem; /* mb-1 */
}

.markdown-content a {
  color: #3898ff; /* text-phantom-blue */
}

.markdown-content a:hover {
  text-decoration: underline; /* hover:underline */
}

.markdown-content strong {
  font-weight: 700; /* font-bold */
  color: white; /* text-white */
}

.markdown-content em {
  font-style: italic; /* italic */
  color: rgba(255, 255, 255, 0.8); /* text-white/80 */
}

.markdown-content code {
  background-color: rgba(255, 255, 255, 0.1); /* bg-white/10 */
  padding: 0.125rem 0.375rem; /* px-1.5 py-0.5 */
  border-radius: 0.25rem; /* rounded */
  color: #3898ff; /* text-phantom-blue */
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace; /* font-mono */
  font-size: 0.875rem; /* text-sm */
}

.markdown-content pre {
  background-color: rgba(255, 255, 255, 0.1); /* bg-white/10 */
  padding: 1rem; /* p-4 */
  border-radius: 0.5rem; /* rounded-lg */
  overflow-x: auto; /* overflow-x-auto */
  margin-bottom: 1rem; /* mb-4 */
}

.markdown-content blockquote {
  border-left-width: 4px; /* border-l-4 */
  border-left-color: rgba(56, 152, 255, 0.5); /* border-phantom-blue/50 */
  padding-left: 1rem; /* pl-4 */
  font-style: italic; /* italic */
  color: rgba(255, 255, 255, 0.7); /* text-white/70 */
  margin-top: 1rem; /* my-4 */
  margin-bottom: 1rem; /* my-4 */
}

.markdown-content hr {
  border-color: rgba(255, 255, 255, 0.1); /* border-white/10 */
  margin-top: 1.5rem; /* my-6 */
  margin-bottom: 1.5rem; /* my-6 */
}

.markdown-content table {
  width: 100%; /* w-full */
  border-collapse: collapse; /* border-collapse */
  margin-bottom: 1rem; /* mb-4 */
}

.markdown-content th,
.markdown-content td {
  border-width: 1px; /* border */
  border-color: rgba(255, 255, 255, 0.1); /* border-white/10 */
  padding: 0.5rem; /* p-2 */
}

.markdown-content th {
  background-color: rgba(255, 255, 255, 0.1); /* bg-white/10 */
}
