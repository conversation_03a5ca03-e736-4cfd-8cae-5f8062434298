/* Common CSS styles for views components */

/* Custom scrollbar for overflow areas */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgba(6, 182, 212, 0.5),
    rgba(59, 130, 246, 0.5)
  );
  border-radius: 4px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(6, 182, 212, 0.7),
    rgba(59, 130, 246, 0.7)
  );
}

/* Add some padding to the right of the content to prevent overlap with scrollbar */
.custom-scrollbar {
  padding-right: 4px;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(6, 182, 212, 0.5) rgba(31, 41, 55, 0.5); /* For Firefox */
}

/* Fade transition for error and success messages */
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.5s,
    transform 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Common height and overflow utilities */
.h-screen {
  height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

/* Shadow glow effect */
.shadow-glow-md {
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.15);
}

/* Message display styles */
.message-success {
  @apply bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl flex items-center;
}

.message-error {
  @apply bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl flex items-center;
}

/* Loading indicator styles */
.loading-indicator {
  @apply flex justify-center items-center py-12;
}

.loading-spinner {
  @apply animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue;
}
