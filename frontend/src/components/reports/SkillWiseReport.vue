<template>
  <div class="bg-gray-900/30 p-6 rounded-lg">
    <div class="space-y-6">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-white">
          Skillwise Performance Heatmap
        </h3>
        <Button
          variant="generalAction"
          size="backButton"
          :disabled="loading"
          class="bg-purple-600 hover:bg-purple-700"
          @click="fetchData"
        >
          {{ loading ? "Loading..." : "Refresh Data" }}
        </Button>
      </div>

      <!-- Error/Success message -->
      <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
        <AlertDescription>{{ message }}</AlertDescription>
      </Alert>

      <!-- Filters Section -->
      <div class="flex flex-col md:flex-row gap-4 justify-start">
        <!-- Skill Filter -->
        <div
          class="bg-gray-800/30 rounded-lg border border-purple-500/30 p-3 md:w-1/3 max-w-md"
        >
          <div class="flex flex-col space-y-1">
            <div class="flex justify-between items-center">
              <label for="skill-filter" class="text-sm font-medium text-white"
                >Filter by Skills:</label
              >
              <div
                v-if="selectedSkills.length > 0"
                class="text-sm text-gray-400"
              >
                {{ selectedSkills.length }} skill{{
                  selectedSkills.length > 1 ? "s" : ""
                }}
                selected
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <Select
                v-model="selectedSkills"
                :disabled="loading || !hasData"
                multiple
                class="w-full"
              >
                <SelectTrigger variant="small" class="w-full bg-gray-800">
                  <SelectValue
                    variant="small"
                    placeholder="Select skills to filter"
                  />
                </SelectTrigger>
                <SelectContent class="bg-gray-800 border-purple-500/50">
                  <div
                    v-if="skills.length > 0"
                    class="py-1.5 px-3 text-sm text-gray-400"
                  >
                    {{ skills.length }} skills available
                  </div>
                  <SelectItem
                    v-for="skill in skills"
                    :key="skill.id"
                    :value="skill.id"
                  >
                    {{ skill.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                :disabled="loading || !hasData || selectedSkills.length === 0"
                class="whitespace-nowrap border-purple-500/50 text-white hover:bg-purple-700/30"
                @click="clearSkillFilter"
              >
                Clear
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Skills List -->
      <div v-if="selectedSkills.length > 0" class="flex justify-start">
        <div
          class="bg-gray-800/30 rounded-lg border border-purple-500/30 p-3 md:w-1/3 max-w-md"
        >
          <div class="flex flex-col">
            <div class="flex items-center mb-2">
              <span class="text-sm font-medium text-white mr-2"
                >Selected Skills:</span
              >
              <span class="text-xs text-gray-400"
                >({{ selectedSkills.length }} selected)</span
              >
            </div>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="skill in filteredSkills"
                :key="skill.id"
                class="px-2 py-1 bg-purple-700/40 rounded-md text-xs text-white flex items-center"
              >
                <span>{{ skill.name }}</span>
                <button
                  class="ml-2 text-gray-300 hover:text-white"
                  title="Remove skill"
                  @click="removeSkill(skill.id)"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Heatmap Container -->
      <div class="bg-gray-800/30 rounded-lg border border-purple-500/30 p-4">
        <div class="flex flex-wrap gap-2 mb-2">
          <div
            v-if="selectedSkills.length > 0"
            class="px-2 py-1 bg-purple-700/30 rounded text-xs text-white inline-flex items-center"
          >
            <span class="mr-1">Skills filter:</span>
            <span class="font-semibold">{{ filteredSkills.length }}</span> of
            {{ skills.length }} skills
          </div>
        </div>
        <div class="h-[600px]">
          <div v-if="loading" class="h-full flex items-center justify-center">
            <div class="text-white">Loading data...</div>
          </div>
          <div
            v-else-if="!hasData"
            class="h-full flex items-center justify-center"
          >
            <div class="text-gray-400">
              No data available. Click "Refresh Data" to load data.
            </div>
          </div>
          <div
            v-else-if="filteredPerformanceData.length === 0"
            class="h-full flex items-center justify-center"
          >
            <div class="text-gray-400">
              <p>No data available for the current filters.</p>
              <p class="mt-2 text-sm">
                {{
                  selectedSkills.length > 0
                    ? "Try selecting different skills or "
                    : ""
                }}
                click "Clear" to reset filters.
              </p>
            </div>
          </div>
          <SkillwiseHeatmap
            v-else
            :users="users"
            :skills="filteredSkills"
            :data="filteredPerformanceData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { api } from "@/services/api";
import { SkillwiseHeatmap } from "../charts";
import { debug } from "@/utils/logger";

const { message, isSuccess, setSuccessMessage, setErrorMessage, clearMessage } =
  useMessageHandler();

// Data states
const loading = ref(false);
const users = ref([]);
const skills = ref([]);
const skillPerformanceData = ref([]);
const selectedSkills = ref([]);

// Computed property to check if we have data to display
const hasData = computed(() => {
  return (
    skillPerformanceData.value.length > 0 &&
    users.value.length > 0 &&
    skills.value.length > 0
  );
});

// Filtered skills based on selection
const filteredSkills = computed(() => {
  if (!selectedSkills.value.length) {
    return skills.value; // Return all skills if none selected
  }
  return skills.value.filter((skill) =>
    selectedSkills.value.includes(skill.id),
  );
});

// Filtered performance data based on selected skills
const filteredPerformanceData = computed(() => {
  let filteredData = skillPerformanceData.value;

  // Filter by skills if any are selected
  if (selectedSkills.value.length > 0) {
    filteredData = filteredData.filter((item) => {
      // If skill_id is available, use it for filtering
      if (item.skill_id !== undefined) {
        return selectedSkills.value.includes(item.skill_id);
      }

      // Otherwise, try to match by skill name
      const selectedSkillNames = filteredSkills.value.map(
        (skill) => skill.name,
      );
      return selectedSkillNames.includes(item.skill_name);
    });
  }

  return filteredData;
});

// Clear skill filter
const clearSkillFilter = () => {
  selectedSkills.value = [];
};

// Remove a single skill from selection
const removeSkill = (skillId) => {
  selectedSkills.value = selectedSkills.value.filter((id) => id !== skillId);
};

// Fetch all required data
const fetchData = async () => {
  loading.value = true;
  clearMessage();
  selectedSkills.value = []; // Reset skill filter when fetching new data

  try {
    // Fetch all data in a single request using the new endpoint
    const response = await api.admin.getSkillwiseHeatmap();
    const data = extractResponseData(response);

    debug("Heatmap response:", { response });
    debug("Extracted data:", { data });

    if (data) {
      // Set users
      if (data.users && Array.isArray(data.users)) {
        users.value = data.users;
      } else {
        users.value = [];
      }

      // Set skills
      if (data.skills && Array.isArray(data.skills)) {
        skills.value = data.skills;
      } else {
        skills.value = [];
      }

      // Set performance data
      if (data.performance_data && Array.isArray(data.performance_data)) {
        skillPerformanceData.value = data.performance_data;
      } else {
        skillPerformanceData.value = [];
      }

      if (skillPerformanceData.value.length > 0) {
        setSuccessMessage("Data loaded successfully");
      } else {
        setErrorMessage("No skill performance data available");
      }
    } else {
      setErrorMessage("Invalid response format from server");
      users.value = [];
      skills.value = [];
      skillPerformanceData.value = [];
    }
  } catch (error) {
    logError(error, "fetchData");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || "Failed to fetch data");
    users.value = [];
    skills.value = [];
    skillPerformanceData.value = [];
  } finally {
    loading.value = false;
  }
};

// Initialize component
onMounted(() => {
  fetchData();
});
</script>
