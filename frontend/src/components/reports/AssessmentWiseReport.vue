<template>
  <div
    class="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800 p-6"
  >
    <!-- Assessment Selection -->
    <div class="space-y-6">
      <div>
        <Label for="assessmentSelect" class="text-white font-medium mb-2 block"
          >Select Assessment</Label
        >
        <Select
          id="assessmentSelect"
          v-model="selectedAssessmentId"
          name="assessmentSelect"
          @update:model-value="onAssessmentChange"
        >
          <SelectTrigger
            variant="small"
            class="bg-gray-800/70 border-gray-700 text-white font-bold"
          >
            <SelectValue variant="small" placeholder="Choose an assessment" />
          </SelectTrigger>
          <SelectContent
            id="assessmentSelectContent"
            class="bg-gray-800 border border-gray-700 max-h-60 overflow-y-auto"
          >
            <template
              v-for="item in assessments || []"
              :key="item?.id_hash || item?.id || item?.name || Math.random()"
            >
              <SelectItem
                v-if="item && (item.id_hash || item.id)"
                :value="(item?.id_hash || item?.id)?.toString() || ''"
                class="text-white hover:bg-gray-700"
              >
                {{ item?.name || "Unnamed Assessment" }}
              </SelectItem>
            </template>
            <div
              v-if="!assessments || assessments.length === 0"
              class="px-4 py-3 text-gray-400 text-sm"
            >
              No assessments available
            </div>
          </SelectContent>
        </Select>
        <p class="text-xs text-gray-400 mt-1">
          Select an assessment to view its skills and questions
        </p>
      </div>

      <!-- Error/Success message -->
      <Alert
        v-if="message"
        :variant="isSuccess ? 'success' : 'error'"
        class="mt-4"
      >
        <AlertDescription>{{ message }}</AlertDescription>
      </Alert>
    </div>

    <!-- Assessment Details Section -->
    <div v-if="selectedAssessment && assessmentDetails" class="mt-8">
      <h3 class="text-lg font-semibold text-white mb-6">Assessment Details</h3>

      <!-- Assessment Info -->
      <div class="bg-gray-800/50 rounded-lg border border-gray-700 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-gray-400 text-sm">Assessment Name</p>
            <p class="text-white font-medium">
              {{ assessmentDetails.name }}
            </p>
          </div>
          <div>
            <p class="text-gray-400 text-sm">Mode</p>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="
                assessmentDetails.question_selection_mode === 'fixed'
                  ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                  : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
              "
            >
              {{
                assessmentDetails.question_selection_mode === "fixed"
                  ? "Fixed"
                  : "Dynamic"
              }}
            </span>
          </div>
          <div>
            <p class="text-gray-400 text-sm">Total Questions</p>
            <p class="text-white font-medium">
              {{ assessmentDetails.total_questions || "N/A" }}
            </p>
          </div>
        </div>
      </div>

      <!-- Skills Section -->
      <div
        v-if="assessmentDetails.skills && assessmentDetails.skills.length > 0"
        class="mb-6"
      >
        <h4 class="text-md font-semibold text-white mb-4">Associated Skills</h4>
        <div class="bg-gray-800/50 rounded-lg border border-gray-700 p-4">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="skill in assessmentDetails.skills"
              :key="skill.id"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30"
            >
              {{ skill.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- Questions Section -->
      <div
        v-if="
          sortedQuestions.length > 0 ||
          (assessmentDetails?.available_questions &&
            assessmentDetails.available_questions.length > 0) ||
          (reportQuestions && reportQuestions.length > 0)
        "
      >
        <div class="flex justify-between items-center mb-4">
          <h4 class="text-md font-semibold text-white">
            <template
              v-if="
                selectedAssessment &&
                selectedAssessment.question_selection_mode === 'fixed'
              "
            >
              All Questions
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 ml-2"
              >
                {{
                  selectedLevel
                    ? sortedQuestions.length
                    : assessmentDetails?.available_questions?.length ||
                      reportQuestions?.length ||
                      0
                }}
              </span>
            </template>
            <template v-else>
              Attended Questions
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-cyan-500/20 text-cyan-400 border border-cyan-500/30 ml-2"
              >
                {{ sortedQuestions.length }}
              </span>
            </template>
            <span
              v-if="assessmentDetails.question_stats"
              class="ml-3 inline-flex items-center space-x-2"
            >
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-green-500/20 text-green-400 border border-green-500/30"
              >
                {{ assessmentDetails.question_stats.available.easy }}
              </span>
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
              >
                {{ assessmentDetails.question_stats.available.intermediate }}
              </span>
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-orange-500/20 text-orange-400 border border-orange-500/30"
              >
                {{ assessmentDetails.question_stats.available.advanced }}
              </span>
            </span>
          </h4>

          <!-- Level Filter Buttons -->
          <div class="flex items-center">
            <span class="text-white text-sm mr-2">Filter by Level:</span>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'easy'
                    ? 'bg-green-500/40 text-green-300 border border-green-500/50'
                    : 'bg-green-500/10 text-green-400 border border-green-500/20 hover:bg-green-500/20'
                "
                @click="toggleLevelFilter('easy')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-green-400 mr-2"
                />
                Easy
              </button>
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'intermediate'
                    ? 'bg-yellow-500/40 text-yellow-300 border border-yellow-500/50'
                    : 'bg-yellow-500/10 text-yellow-400 border border-yellow-500/20 hover:bg-yellow-500/20'
                "
                @click="toggleLevelFilter('intermediate')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-yellow-400 mr-2"
                />
                Intermediate
              </button>
              <button
                class="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                :class="
                  selectedLevel === 'advanced'
                    ? 'bg-orange-500/40 text-orange-300 border border-orange-500/50'
                    : 'bg-orange-500/10 text-orange-400 border border-orange-500/20 hover:bg-orange-500/20'
                "
                @click="toggleLevelFilter('advanced')"
              >
                <span
                  class="inline-block w-2 h-2 rounded-full bg-orange-400 mr-2"
                />
                Advanced
              </button>
            </div>
          </div>
        </div>

        <!-- Questions List -->
        <div
          class="bg-gray-800/50 rounded-lg border border-gray-700 overflow-hidden"
        >
          <!-- Loading indicator for question statistics -->
          <div
            v-if="questionStatsLoading"
            class="flex items-center justify-center py-8"
          >
            <SpinnerIcon class="w-6 h-6 mr-2 text-cyan-500" />
            <span class="text-white">Loading question statistics...</span>
          </div>

          <div v-else class="max-h-96 overflow-y-auto">
            <table class="w-full">
              <thead class="bg-gray-700/50 sticky top-0">
                <tr>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Skill
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Level
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Question
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Total Users
                  </th>
                  <th class="px-4 py-3 text-left text-white font-medium">
                    Correct %
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="question in sortedQuestions"
                  :key="question.que_id"
                  class="border-t border-gray-700/50 hover:bg-gray-700/30"
                >
                  <td class="px-4 py-3">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30"
                    >
                      {{ question.skill_name }}
                    </span>
                  </td>
                  <td class="px-4 py-3">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="{
                        'bg-green-500/20 text-green-400 border border-green-500/30':
                          question.level === 'easy',
                        'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30':
                          question.level === 'intermediate',
                        'bg-orange-500/20 text-orange-400 border border-orange-500/30':
                          question.level === 'advanced',
                      }"
                    >
                      {{ question.level }}
                    </span>
                  </td>
                  <td class="px-4 py-3 text-gray-300 text-sm max-w-xs">
                    <div class="truncate" :title="question.question">
                      {{ question.question }}
                    </div>
                  </td>
                  <td class="px-4 py-3 text-white text-sm">
                    {{ calculateTotalUsers(question) }}
                  </td>
                  <td class="px-4 py-3 text-white text-sm">
                    <span
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                      :class="{
                        'bg-red-500/20 text-red-400 border border-red-500/30':
                          (question.correct_percentage || 0) < 50,
                        'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30':
                          (question.correct_percentage || 0) >= 50 &&
                          (question.correct_percentage || 0) < 75,
                        'bg-green-500/20 text-green-400 border border-green-500/30':
                          (question.correct_percentage || 0) >= 75,
                      }"
                    >
                      {{ (question.correct_percentage || 0).toFixed(1) }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- No Questions Message -->
      <div
        v-else-if="selectedAssessment && assessmentDetails"
        class="text-center py-8"
      >
        <div
          class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-700/50 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-8 w-8 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <template
          v-if="
            (!assessmentDetails?.available_questions ||
              assessmentDetails.available_questions.length === 0) &&
            (!reportQuestions || reportQuestions.length === 0)
          "
        >
          <h3 class="text-lg font-medium text-white mb-2">
            No Questions Available
          </h3>
          <p class="text-gray-400">
            This assessment has no associated questions.
          </p>
        </template>
        <template
          v-else-if="
            selectedAssessment &&
            selectedAssessment.question_selection_mode === 'dynamic' &&
            (!reportQuestions || reportQuestions.length === 0)
          "
        >
          <h3 class="text-lg font-medium text-white mb-2">
            No Attended Questions
          </h3>
          <p class="text-gray-400">
            No users have attended questions from this dynamic assessment yet.
          </p>
        </template>
      </div>

      <!-- Generate Report Button -->
      <div class="flex justify-end mt-4">
        <Button
          :disabled="assessmentWiseLoading"
          variant="generalAction"
          size="backButton"
          @click="generateAssessmentWiseReport"
        >
          <SpinnerIcon v-if="assessmentWiseLoading" class="w-3 h-3 mr-1" />
          {{ assessmentWiseLoading ? "Generating..." : "Generate Report" }}
        </Button>
      </div>
    </div>

    <!-- Download Cards for Assessment-wise Report -->
    <div v-if="assessmentWiseReportGenerated" class="mt-8">
      <h3 class="text-lg font-semibold text-white mb-4">Download Reports</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Base Report Download Card -->
        <div
          v-if="assessmentWiseReports.baseReportUrl"
          class="bg-gray-800/50 rounded-lg border border-cyan-500/30 p-4 hover:bg-gray-800/70 transition-colors"
        >
          <div class="flex items-center">
            <div class="bg-cyan-500/10 p-2 rounded-lg mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-cyan-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-white font-medium">Base Report</p>
              <p class="text-xs text-gray-400">
                Contains detailed assessment data for
                {{ selectedAssessment?.name || "selected assessment" }}
              </p>
            </div>
            <Button
              :as="'a'"
              :href="assessmentWiseReports.baseReportUrl"
              download
              variant="generalAction"
              size="backButton"
              class="bg-cyan-600 hover:bg-cyan-700"
            >
              Download
            </Button>
          </div>
        </div>

        <!-- Score Report Download Card -->
        <div
          v-if="assessmentWiseReports.scoreReportUrl"
          class="bg-gray-800/50 rounded-lg border border-cyan-500/30 p-4 hover:bg-gray-800/70 transition-colors"
        >
          <div class="flex items-center">
            <div class="bg-cyan-500/10 p-2 rounded-lg mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-cyan-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-white font-medium">Score Report</p>
              <p class="text-xs text-gray-400">
                Contains score analysis and statistics for
                {{ selectedAssessment?.name || "selected assessment" }}
              </p>
            </div>
            <Button
              :as="'a'"
              :href="assessmentWiseReports.scoreReportUrl"
              download
              variant="generalAction"
              size="backButton"
              class="bg-cyan-600 hover:bg-cyan-700"
            >
              Download
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SpinnerIcon } from "@/components/icons";
import { useMessageHandler } from "@/utils/messageHandler";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { api } from "@/services/api";
import { debug, warning, error } from "@/utils/logger";
import config from "../../config/globalConfig";

const { message, isSuccess, setSuccessMessage, setErrorMessage, clearMessage } =
  useMessageHandler();

// State variables
const assessments = ref([]);
const selectedAssessmentId = ref("");
const selectedAssessment = ref(null);
const assessmentDetails = ref(null);
const selectedLevel = ref(""); // New ref for level filter
const assessmentWiseLoading = ref(false);
const questionStatsLoading = ref(false);
const assessmentWiseReportGenerated = ref(false);
const assessmentWiseReports = ref({
  baseReportUrl: "",
  scoreReportUrl: "",
});
const reportQuestions = ref([]);

// Computed property to sort questions by difficulty level and filter by selected level
const sortedQuestions = computed(() => {
  if (!selectedAssessment.value) {
    return [];
  }

  const levelOrder = { easy: 1, intermediate: 2, advanced: 3 };
  const isFixedAssessment =
    selectedAssessment.value.question_selection_mode === "fixed";

  // For fixed assessments: show all questions with statistics merged
  if (isFixedAssessment) {
    // Use available_questions from assessmentDetails if available, otherwise use reportQuestions
    const hasAvailableQuestions =
      assessmentDetails.value?.available_questions &&
      assessmentDetails.value.available_questions.length > 0;
    const hasReportQuestions =
      reportQuestions.value && reportQuestions.value.length > 0;

    if (hasAvailableQuestions) {
      const allQuestions = [...assessmentDetails.value.available_questions];

      // Merge statistics from reportQuestions if available
      if (hasReportQuestions) {
        allQuestions.forEach((question) => {
          const reportQuestion = reportQuestions.value.find(
            (rq) => rq.que_id === question.que_id,
          );
          if (reportQuestion) {
            question.total_attended_users =
              reportQuestion.total_attended_users || 0;
            question.correct_percentage =
              reportQuestion.correct_percentage || 0;
            question.correct_answers = reportQuestion.correct_answers || 0;
            question.total_attempts = reportQuestion.total_attempts || 0;
          } else {
            // No statistics available for this question
            question.total_attended_users = 0;
            question.correct_percentage = 0;
            question.correct_answers = 0;
            question.total_attempts = 0;
          }
        });
      } else {
        // No report data yet, set all statistics to 0
        allQuestions.forEach((question) => {
          question.total_attended_users = 0;
          question.correct_percentage = 0;
          question.correct_answers = 0;
          question.total_attempts = 0;
        });
      }

      // Filter by level if selected
      const filteredQuestions =
        selectedLevel.value && selectedLevel.value !== ""
          ? allQuestions.filter((q) => q.level === selectedLevel.value)
          : allQuestions;

      return filteredQuestions.sort((a, b) => {
        const levelA = levelOrder[a.level] || 999;
        const levelB = levelOrder[b.level] || 999;
        return levelA - levelB;
      });
    } else if (hasReportQuestions) {
      // Fall back to reportQuestions if available_questions is not available
      const dynamicQuestions = [...reportQuestions.value];

      // Filter by level if selected
      const filteredQuestions =
        selectedLevel.value && selectedLevel.value !== ""
          ? dynamicQuestions.filter((q) => q.level === selectedLevel.value)
          : dynamicQuestions;

      return filteredQuestions.sort((a, b) => {
        const levelA = levelOrder[a.level] || 999;
        const levelB = levelOrder[b.level] || 999;
        return levelA - levelB;
      });
    }

    return [];
  }

  // For dynamic assessments: show only attended questions (from report data)
  if (reportQuestions.value && reportQuestions.value.length > 0) {
    const dynamicQuestions = [...reportQuestions.value];

    // Filter by level if selected
    const filteredQuestions =
      selectedLevel.value && selectedLevel.value !== ""
        ? dynamicQuestions.filter((q) => q.level === selectedLevel.value)
        : dynamicQuestions;

    return filteredQuestions.sort((a, b) => {
      const levelA = levelOrder[a.level] || 999;
      const levelB = levelOrder[b.level] || 999;
      return levelA - levelB;
    });
  }

  // If no report data for dynamic assessment, show empty
  return [];
});

// Fetch all assessments on component mount
const fetchAssessments = async () => {
  try {
    clearMessage();
    debug("Fetching assessments...");
    const response = await api.admin.getAssessments({ limit: 100, offset: 0 });
    debug("Raw API response:", { response });

    const assessmentData = extractResponseData(response);
    debug("Extracted assessment data:", { assessmentData });

    const assessmentList = assessmentData?.assessments || assessmentData || [];
    debug("Assessment list before filtering:", { assessmentList });

    // Ensure we have an array and filter out any invalid assessments
    if (Array.isArray(assessmentList)) {
      assessments.value = assessmentList.filter(
        (assessment) =>
          assessment &&
          typeof assessment === "object" &&
          ((assessment.id !== undefined && assessment.id !== null) ||
            (assessment.id_hash !== undefined && assessment.id_hash !== null)),
      );
      debug("Filtered assessments with id or id_hash:", {
        assessments: assessments.value,
      });
    } else {
      warning("Assessment list is not an array, setting empty array");
      assessments.value = [];
    }

    // If we have no assessments after filtering, show a message
    if (assessments.value.length === 0) {
      setErrorMessage("No assessments found. Please create assessments first.");
    }
  } catch (fetchError) {
    error("Error fetching assessments:", { error: fetchError });
    logError(fetchError, "fetchAssessments");
    const errorInfo = extractErrorInfo(fetchError);
    debug("Error info:", { errorInfo });
    setErrorMessage(errorInfo.message || "Failed to fetch assessments");
    // Ensure assessments is always an array, even on error
    assessments.value = [];
  }
};

// Handle assessment selection change
const onAssessmentChange = async (assessmentId) => {
  if (!assessmentId) {
    selectedAssessment.value = null;
    assessmentDetails.value = null;
    reportQuestions.value = [];
    selectedLevel.value = ""; // Reset level filter
    assessmentWiseReportGenerated.value = false;
    assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };
    return;
  }

  try {
    clearMessage();

    // Clear previous report data
    reportQuestions.value = [];
    selectedLevel.value = ""; // Reset level filter
    assessmentWiseReportGenerated.value = false;
    assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };

    // Find the selected assessment
    selectedAssessment.value = assessments.value.find(
      (a) =>
        a &&
        ((a.id_hash && a.id_hash.toString() === assessmentId) ||
          (a.id && a.id.toString() === assessmentId)),
    );

    // Fetch detailed assessment information including skills and questions
    // Use the ID or ID_HASH depending on what's available
    const response = await api.admin.getAssessment(assessmentId);
    assessmentDetails.value = extractResponseData(response);

    // Fetch question statistics immediately
    await fetchQuestionStatistics();
  } catch (fetchError) {
    error("Error fetching assessment details:", { error: fetchError });
    logError(fetchError, "onAssessmentChange");
    setErrorMessage(
      getErrorMessage(fetchError, "Failed to fetch assessment details"),
    );
    selectedAssessment.value = null;
    assessmentDetails.value = null;
    reportQuestions.value = [];
    selectedLevel.value = ""; // Reset level filter
  }
};

// Fetch question statistics for the selected assessment
const fetchQuestionStatistics = async () => {
  if (!selectedAssessment.value) {
    return;
  }

  questionStatsLoading.value = true;

  try {
    // Extract the base name from the assessment name
    // Remove common suffixes like "Assessment", "Mock Assessment", "Final Assessment"
    let assessmentBaseName = selectedAssessment.value.name
      .replace(/\s+(Mock|Final)?\s*Assessment$/i, "")
      .trim();

    // Determine quiz type based on assessment properties
    const quizType =
      selectedAssessment.value.question_selection_mode === "fixed"
        ? config.defaultQuizType.final
        : config.defaultQuizType.mock;

    const response = await api.admin.generateReport({
      report_type: "assessment_wise",
      assessment_base_name: assessmentBaseName,
      quiz_type: quizType,
    });

    // Store the question statistics for display
    const reportData = extractResponseData(response);
    if (reportData?.base_report && Array.isArray(reportData.base_report)) {
      reportQuestions.value = reportData.base_report;
    }
  } catch (fetchError) {
    error("Error fetching question statistics:", { error: fetchError });
    logError(fetchError, "fetchQuestionStatistics");
    // Don't show error message as this is background loading
    // If no statistics are available, questions will show 0 values
  } finally {
    questionStatsLoading.value = false;
  }
};

// Generate Assessment-wise Report
const generateAssessmentWiseReport = async () => {
  if (!selectedAssessment.value) {
    setErrorMessage("Please select an assessment");
    return;
  }

  assessmentWiseLoading.value = true;
  clearMessage();
  assessmentWiseReportGenerated.value = false;
  assessmentWiseReports.value = { baseReportUrl: "", scoreReportUrl: "" };

  try {
    // Extract the base name from the assessment name
    // Remove common suffixes like "Assessment", "Mock Assessment", "Final Assessment"
    let assessmentBaseName = selectedAssessment.value.name
      .replace(/\s+(Mock|Final)?\s*Assessment$/i, "")
      .trim();

    // Determine quiz type based on assessment properties
    const quizType =
      selectedAssessment.value.question_selection_mode === "fixed"
        ? config.defaultQuizType.final
        : config.defaultQuizType.mock;

    const response = await api.admin.generateReport({
      report_type: "assessment_wise",
      assessment_base_name: assessmentBaseName,
      quiz_type: quizType,
    });

    // Use standardized response data extraction
    const responseData = extractResponseData(response);

    if (
      !responseData ||
      (!responseData.base_report && !responseData.score_report)
    ) {
      const errorMessage = responseData?.message || "No report data available";
      setErrorMessage(errorMessage);
      return;
    }

    assessmentWiseReportGenerated.value = true;
    setSuccessMessage("Assessment-wise report generated successfully!");

    // Update the question statistics for display (in case data has changed)
    if (responseData.base_report && Array.isArray(responseData.base_report)) {
      reportQuestions.value = responseData.base_report;
    }

    // Create blob URLs for CSV downloads
    if (responseData.base_report) {
      let csvContent;
      if (Array.isArray(responseData.base_report)) {
        // Convert JSON to CSV
        const headers = [
          "Skill",
          "Level",
          "Question",
          "Total Users",
          "Correct Answers",
          "Total Attempts",
          "Correct %",
        ];
        const csvRows = [headers.join(",")];

        responseData.base_report.forEach((row) => {
          // Calculate total users who attempted (same logic as in template)
          let totalUsers = 0;
          if (
            row.correct_answers &&
            row.correct_percentage &&
            row.correct_percentage > 0
          ) {
            totalUsers = Math.round(
              row.correct_answers / (row.correct_percentage / 100),
            );
          } else if (row.total_attempts) {
            totalUsers = row.total_attempts;
          } else {
            totalUsers = row.total_attended_users || 0;
          }

          const csvRow = [
            `"${row.skill_name || ""}"`,
            `"${row.level || ""}"`,
            `"${(row.question || "").replace(/"/g, '""')}"`,
            totalUsers,
            row.correct_answers || 0,
            row.total_attempts || 0,
            `${(row.correct_percentage || 0).toFixed(1)}%`,
          ];
          csvRows.push(csvRow.join(","));
        });

        csvContent = csvRows.join("\n");
      } else {
        csvContent = responseData.base_report;
      }

      const baseReportBlob = new Blob([csvContent], { type: "text/csv" });
      assessmentWiseReports.value.baseReportUrl =
        URL.createObjectURL(baseReportBlob);
    }

    if (responseData.score_report) {
      let csvContent;
      if (Array.isArray(responseData.score_report)) {
        // Convert JSON to CSV
        const headers = [
          "User ID",
          "Topic",
          "Total Score",
          "Obtained Score",
          "Percentage",
          "Performance Level",
          "Easy Attempted",
          "Easy Correct",
          "Intermediate Attempted",
          "Intermediate Correct",
          "Advanced Attempted",
          "Advanced Correct",
        ];
        const csvRows = [headers.join(",")];

        responseData.score_report.forEach((row) => {
          const csvRow = [
            `"${row.user_id || ""}"`,
            `"${row.topic || ""}"`,
            row.total_score || 0,
            row.obtained_score || 0,
            `${(row.percentage || 0).toFixed(1)}%`,
            `"${row.performance_level || ""}"`,
            row.easy_attempted || 0,
            row.easy_correct || 0,
            row.intermediate_attempted || 0,
            row.intermediate_correct || 0,
            row.advanced_attempted || 0,
            row.advanced_correct || 0,
          ];
          csvRows.push(csvRow.join(","));
        });

        csvContent = csvRows.join("\n");
      } else {
        csvContent = responseData.score_report;
      }

      const scoreReportBlob = new Blob([csvContent], { type: "text/csv" });
      assessmentWiseReports.value.scoreReportUrl =
        URL.createObjectURL(scoreReportBlob);
    }
  } catch (generateError) {
    error("Error generating assessment-wise report:", { error: generateError });
    logError(generateError, "generateAssessmentWiseReport");
    setErrorMessage(
      getErrorMessage(
        generateError,
        "Failed to generate assessment-wise report",
      ),
    );
  } finally {
    assessmentWiseLoading.value = false;
  }
};

// Calculate total users who attempted a question (regardless of correctness)
const calculateTotalUsers = (question) => {
  // If we have correct answers and percentage, calculate total users
  if (
    question.correct_answers &&
    question.correct_percentage &&
    question.correct_percentage > 0
  ) {
    return Math.round(
      question.correct_answers / (question.correct_percentage / 100),
    );
  }

  // If no correct answers but we have total_attempts, use that as fallback
  if (question.total_attempts) {
    return question.total_attempts;
  }

  // Fallback to total_attended_users (might be incorrect but better than 0)
  return question.total_attended_users || 0;
};

// Toggle level filter on/off
const toggleLevelFilter = (level) => {
  // If the same level is clicked again, clear the filter
  if (selectedLevel.value === level) {
    selectedLevel.value = "";
  } else {
    // Otherwise, set the filter to the selected level
    selectedLevel.value = level;
  }
};

// Initialize component
onMounted(() => {
  fetchAssessments();
});
</script>
