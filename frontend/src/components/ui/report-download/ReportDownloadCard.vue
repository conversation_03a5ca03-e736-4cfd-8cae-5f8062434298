<template>
  <div class="flex items-center p-3 bg-gray-800 rounded-lg">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6 mr-3"
      :class="iconColor"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        :d="iconPath"
      />
    </svg>
    <div class="flex-1">
      <p class="text-white">
        {{ title }}
      </p>
      <p class="text-xs text-gray-400">
        {{ description }}
      </p>
    </div>
    <Button
      as="a"
      :href="downloadUrl"
      download
      :variant="buttonVariant"
      size="sm"
    >
      Download
    </Button>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { Button } from "@/components/ui/button";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  downloadUrl: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: "base", // 'base' or 'score'
    validator: (value) => ["base", "score"].includes(value),
  },
  color: {
    type: String,
    default: "cyan", // 'cyan', 'teal', 'purple', 'indigo', 'blue'
    validator: (value) =>
      ["cyan", "teal", "purple", "indigo", "blue"].includes(value),
  },
});

const iconPath = computed(() => {
  return props.type === "base"
    ? "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
    : "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z";
});

const iconColor = computed(() => {
  return {
    "text-cyan-500": props.color === "cyan",
    "text-teal-500": props.color === "teal",
    "text-purple-500": props.color === "purple",
    "text-indigo-500": props.color === "indigo",
    "text-blue-500": props.color === "blue",
  };
});

const buttonVariant = computed(() => {
  switch (props.color) {
    case "cyan":
      return "cyan";
    case "teal":
      return "teal";
    case "purple":
      return "purple";
    case "indigo":
      return "indigo";
    case "blue":
      return "blue";
    default:
      return "default";
  }
});
</script>
