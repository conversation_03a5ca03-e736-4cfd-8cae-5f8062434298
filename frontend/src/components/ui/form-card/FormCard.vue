<template>
  <div class="max-w-3xl mx-auto">
    <Card variant="form" :color="color" hover>
      <slot />
    </Card>
  </div>
</template>

<script setup>
import { Card } from "@/components/ui/card";

const props = defineProps({
  color: {
    type: String,
    default: "cyan", // 'cyan', 'teal', 'purple', 'indigo', 'blue'
    validator: (value) =>
      ["cyan", "teal", "purple", "indigo", "blue"].includes(value),
  },
});
</script>
