<template>
  <div class="instructions-tooltip-container">
    <button class="instructions-tooltip-button" @click="toggleModal">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 01.67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 11-.671-1.34l.041-.022zM12 9a.75.75 0 100-********* 0 000 1.5z"
          clip-rule="evenodd"
        />
      </svg>
      <span class="instructions-tooltip">Instructions</span>
    </button>

    <!-- Instructions Modal -->
    <div
      v-if="showModal"
      class="instructions-modal-overlay"
      @click="closeModal"
    >
      <div class="instructions-modal" @click.stop>
        <div class="instructions-header">
          <h3 class="instructions-title">📋 Quiz Session Instructions</h3>
          <button class="close-button" @click="closeModal">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div class="instructions-content">
          <div class="instruction-step">
            <div class="step-icon">✅</div>
            <div class="step-content">
              <h4 class="step-title">Check Your Pending Session</h4>
              <p class="step-description">
                Go to your dashboard and look for any Pending Assessments.
              </p>
            </div>
          </div>

          <div class="instruction-step">
            <div class="step-icon">▶️</div>
            <div class="step-content">
              <h4 class="step-title">Click on "Start"</h4>
              <p class="step-description">
                Begin your quiz by clicking the Start button.
              </p>
            </div>
          </div>

          <div class="instruction-step warning">
            <div class="step-icon">⚠️</div>
            <div class="step-content">
              <h4 class="step-title">Do Not Switch Tabs or Windows</h4>
              <p class="step-description">
                Once the quiz starts, avoid any other activity (like switching
                tabs, refreshing, or opening new windows).
              </p>
              <p class="step-warning">
                ⚠️ If you do, your quiz will be automatically submitted.
              </p>
            </div>
          </div>

          <div class="instruction-step">
            <div class="step-icon">⌨️</div>
            <div class="step-content">
              <h4 class="step-title">Avoid Pressing Unnecessary Keys</h4>
              <p class="step-description">
                Pressing unwanted keys or shortcuts may be tracked and can lead
                to automatic quiz submission.
              </p>
            </div>
          </div>

          <div class="instruction-step">
            <div class="step-icon">⏳</div>
            <div class="step-content">
              <h4 class="step-title">
                Answer All Questions Within the Given Time
              </h4>
              <p class="step-description">
                You must complete all questions within the allotted time.
                Unanswered questions will be marked as incorrect.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const showModal = ref(false);

const toggleModal = () => {
  showModal.value = !showModal.value;
};

const closeModal = () => {
  showModal.value = false;
};
</script>

<style scoped>
/* Tooltip Button Styles */
.instructions-tooltip-container {
  position: relative;
  display: inline-block;
}

.instructions-tooltip-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background-color: #06b6d4;
  background-image: linear-gradient(147deg, #06b6d4 0%, #3b82f6 74%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.151);
}

.instructions-tooltip-button svg {
  height: 1.5em;
  fill: white;
}

.instructions-tooltip-button:hover svg {
  animation: jello-vertical 0.7s both;
}

@keyframes jello-vertical {
  0% {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(0.75, 1.25, 1);
  }
  40% {
    transform: scale3d(1.25, 0.75, 1);
  }
  50% {
    transform: scale3d(0.85, 1.15, 1);
  }
  65% {
    transform: scale3d(1.05, 0.95, 1);
  }
  75% {
    transform: scale3d(0.95, 1.05, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

.instructions-tooltip {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  background-color: #06b6d4;
  background-image: linear-gradient(147deg, #06b6d4 0%, #3b82f6 74%);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  display: none;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  pointer-events: none;
  letter-spacing: 0.5px;
  white-space: nowrap;
  visibility: hidden;
  z-index: 1000;
}

.instructions-tooltip::before {
  position: absolute;
  content: "";
  width: 10px;
  height: 10px;
  background-color: #3b82f6;
  background-size: 1000%;
  background-position: center;
  transform: rotate(45deg);
  bottom: -15%;
  left: 50%;
  margin-left: -5px;
  transition-duration: 0.3s;
}

.instructions-tooltip-container:hover .instructions-tooltip {
  top: -40px;
  opacity: 1;
  visibility: visible;
  display: flex;
  transition: all 0.3s ease;
}

/* Modal Styles */
.instructions-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.instructions-modal {
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 900px;
  width: 90%;
  height: auto;
  max-height: none;
  overflow: visible;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 24px;
}

.instructions-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.instructions-content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.instruction-step {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.instruction-step:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(6, 182, 212, 0.3);
}

.instruction-step.warning {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.05);
}

.instruction-step.warning:hover {
  background: rgba(251, 191, 36, 0.08);
  border-color: rgba(251, 191, 36, 0.5);
}

.step-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
}

.step-description {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

.step-warning {
  color: #fbbf24;
  font-weight: 500;
  margin: 8px 0 0 0;
}

/* Responsive Design */
@media (max-width: 640px) {
  .instructions-modal {
    margin: 10px;
    width: 95%;
    max-width: none;
    max-height: none;
  }

  .instructions-header {
    padding: 16px 16px 0 16px;
  }

  .instructions-content {
    padding: 0 16px 16px 16px;
  }

  .instruction-step {
    padding: 16px;
    gap: 12px;
  }

  .step-icon {
    width: 32px;
    height: 32px;
    font-size: 1.2rem;
  }
}
</style>
