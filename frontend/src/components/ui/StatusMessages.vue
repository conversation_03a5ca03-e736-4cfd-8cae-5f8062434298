<template>
  <div class="space-y-4">
    <!-- Loading State -->
    <div
      v-if="isLoading"
      class="flex justify-center items-center py-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl"
    >
      <div
        class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
      />
      <span class="ml-3 text-white/80">{{
        loadingMessage || "Loading..."
      }}</span>
    </div>

    <!-- Error Message -->
    <div
      v-if="message && !isSuccess"
      class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl flex items-start"
    >
      <svg
        class="w-5 h-5 mr-3 text-red-400 flex-shrink-0 mt-0.5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
      <div class="flex-1">
        <p class="font-medium">Error</p>
        <p class="text-sm text-white/80 mt-1">
          {{ message }}
        </p>
      </div>
    </div>

    <!-- Success Message -->
    <div
      v-if="message && isSuccess"
      class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl flex items-start"
    >
      <svg
        class="w-5 h-5 mr-3 text-green-400 flex-shrink-0 mt-0.5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 13l4 4L19 7"
        />
      </svg>
      <div class="flex-1">
        <p class="font-medium">Success</p>
        <p class="text-sm text-white/80 mt-1">
          {{ message }}
        </p>
      </div>
    </div>

    <!-- Warning Message -->
    <div
      v-if="warningMessage"
      class="bg-yellow-500/10 backdrop-blur-sm border border-yellow-500/30 text-white px-6 py-4 rounded-xl flex items-start"
    >
      <svg
        class="w-5 h-5 mr-3 text-yellow-400 flex-shrink-0 mt-0.5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
      <div class="flex-1">
        <p class="font-medium">Warning</p>
        <p class="text-sm text-white/80 mt-1">
          {{ warningMessage }}
        </p>
      </div>
    </div>

    <!-- Info Message -->
    <div
      v-if="infoMessage"
      class="bg-blue-500/10 backdrop-blur-sm border border-blue-500/30 text-white px-6 py-4 rounded-xl flex items-start"
    >
      <svg
        class="w-5 h-5 mr-3 text-blue-400 flex-shrink-0 mt-0.5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <div class="flex-1">
        <p class="font-medium">Information</p>
        <p class="text-sm text-white/80 mt-1">
          {{ infoMessage }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
/**
 * Status Messages Component
 * Reusable component for displaying different types of status messages
 */
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  loadingMessage: {
    type: String,
    default: "Loading...",
  },
  message: {
    type: String,
    default: "",
  },
  isSuccess: {
    type: Boolean,
    default: false,
  },
  warningMessage: {
    type: String,
    default: "",
  },
  infoMessage: {
    type: String,
    default: "",
  },
});
</script>
