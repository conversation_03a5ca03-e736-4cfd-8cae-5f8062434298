<script setup>
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-vue-next";
import { SelectIcon, SelectTrigger, useForwardProps } from "reka-ui";
import { computed, toRefs } from "vue";

const props = defineProps({
  disabled: { type: Boolean, required: false },
  reference: { type: null, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  id: { type: String, required: false },
  variant: { type: String, default: "default" },
});

// Use toRefs to make props reactive before passing to useForwardProps

// Extract class separately and pass the rest directly to useForwardProps
const { class: className, ...restProps } = toRefs(props);
const forwardedProps = useForwardProps(restProps);
</script>

<template>
  <SelectTrigger
    v-bind="forwardedProps"
    :class="
      cn(
        'flex w-full items-center justify-between whitespace-nowrap rounded-md border bg-transparent shadow-sm ring-offset-background focus:outline-none focus:ring-1 transition-colors duration-200 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:truncate text-start text-white',
        {
          'h-9 border-gray-700 px-3 py-2 text-sm data-[placeholder]:text-muted-foreground focus:ring-cyan-500 hover:border-cyan-500':
            props.variant === 'default',
          'h-8 border-purple-500/50 px-3 py-1.5 text-sm data-[placeholder]:text-gray-400 focus:ring-purple-500 hover:border-purple-400':
            props.variant === 'small',
        },
        className?.value,
      )
    "
  >
    <slot />
    <SelectIcon as-child>
      <ChevronDown class="w-4 h-4 opacity-50 shrink-0" />
    </SelectIcon>
  </SelectTrigger>
</template>
