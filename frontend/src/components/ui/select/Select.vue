<script setup>
import { SelectRoot, useForwardPropsEmits } from "reka-ui";
import { toRefs } from "vue";

const props = defineProps({
  open: { type: Boolean, required: false },
  defaultOpen: { type: Boolean, required: false },
  defaultValue: { type: null, required: false },
  modelValue: { type: null, required: false },
  by: { type: [String, Function], required: false },
  dir: { type: String, required: false },
  multiple: { type: Boolean, required: false },
  autocomplete: { type: String, required: false },
  disabled: { type: Boolean, required: false },
  name: { type: String, required: false },
  required: { type: Boolean, required: false },
  id: { type: String, required: false },
});
const emits = defineEmits(["update:modelValue", "update:open"]);

// Use toRefs to make props reactive before passing to useForwardPropsEmits
const reactiveProps = toRefs(props);
const forwarded = useForwardPropsEmits(reactiveProps, emits);
</script>

<template>
  <SelectRoot v-bind="forwarded">
    <slot />
  </SelectRoot>
</template>
