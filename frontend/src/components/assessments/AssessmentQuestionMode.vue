<template>
  <section
    class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8"
  >
    <h2
      class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
    >
      Question Selection Mode
    </h2>

    <div>
      <label class="block text-white font-medium mb-3">
        How should questions be selected?
      </label>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Dynamic Mode -->
        <div
          class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group"
          :class="{
            'border-phantom-blue': questionSelectionMode === 'dynamic',
          }"
          @click="updateMode('dynamic')"
        >
          <div class="flex items-center">
            <div class="relative flex items-center justify-center">
              <input
                id="dynamicMode"
                :checked="questionSelectionMode === 'dynamic'"
                type="radio"
                name="questionSelectionMode"
                value="dynamic"
                class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                @change="updateMode('dynamic')"
              />
              <div
                v-if="questionSelectionMode === 'dynamic'"
                class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"
              />
            </div>
            <label
              for="dynamicMode"
              class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors"
            >
              Dynamic Mode
            </label>
          </div>
          <div class="mt-3 ml-8 text-sm text-white/80">
            Questions are randomly selected from the skill's question pool for
            each session.
          </div>
        </div>

        <!-- Fixed Mode -->
        <div
          class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group"
          :class="{ 'border-phantom-blue': questionSelectionMode === 'fixed' }"
          @click="updateMode('fixed')"
        >
          <div class="flex items-center">
            <div class="relative flex items-center justify-center">
              <input
                id="fixedMode"
                :checked="questionSelectionMode === 'fixed'"
                type="radio"
                name="questionSelectionMode"
                value="fixed"
                class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                @change="updateMode('fixed')"
              />
              <div
                v-if="questionSelectionMode === 'fixed'"
                class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"
              />
            </div>
            <label
              for="fixedMode"
              class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors"
            >
              Fixed Mode
            </label>
          </div>
          <div class="mt-3 ml-8 text-sm text-white/80">
            Same questions for all sessions. You'll select specific questions
            below.
          </div>
        </div>
      </div>

      <!-- Information Note -->
      <div
        class="mt-6 text-sm text-white/90 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5"
      >
        <p class="font-medium text-phantom-indigo">Important Note:</p>
        <ul class="list-disc list-inside mt-2 space-y-1 ml-2">
          <li>
            <strong>Dynamic mode:</strong> Questions are randomly selected from
            the skill's question pool for each session.
          </li>
          <li>
            <strong>Fixed mode:</strong> After creating the assessment, you'll
            need to go to "Add Fixed Questions" to select specific questions
            that will be used for all sessions.
          </li>
        </ul>
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Question Selection Mode Component
 * Handles the selection between dynamic and fixed question modes
 */
import { logUserAction } from "@/utils/logger";

const props = defineProps({
  questionSelectionMode: {
    type: String,
    default: "dynamic",
  },
});

const emit = defineEmits(["update:questionSelectionMode"]);

const updateMode = (mode) => {
  emit("update:questionSelectionMode", mode);

  logUserAction("question_mode_changed", {
    previousMode: props.questionSelectionMode,
    newMode: mode,
  });
};
</script>

<style scoped>
.animate-ping-slow {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
</style>
