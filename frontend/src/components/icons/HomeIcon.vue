<template>
  <Home
    :size="size"
    :class="$attrs.class"
    :stroke-width="strokeWidth"
    v-bind="$attrs"
  />
</template>

<script setup>
import { Home } from "lucide-vue-next";

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  size: {
    type: [String, Number],
    default: 24,
  },
  strokeWidth: {
    type: [String, Number],
    default: 2,
  },
});

// Methods for animation (if needed)
const startAnimation = () => {
  // Animation logic can be added here if needed
};

const stopAnimation = () => {
  // Animation logic can be added here if needed
};

// Expose methods for parent component
defineExpose({
  startAnimation,
  stopAnimation,
});
</script>

<style scoped>
/* Add any custom styles here */
</style>
