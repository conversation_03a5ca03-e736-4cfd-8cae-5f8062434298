<template>
  <div :class="noScroll ? 'h-screen overflow-hidden' : 'min-h-screen'">
    <!-- Main Content - Header and Footer are now in App.vue -->
    <main
      :class="noScroll ? 'h-full overflow-hidden pt-6 pb-6' : 'pt-10 pb-10'"
    >
      <div
        class="w-[90vw] mx-auto"
        :class="noScroll ? 'h-full overflow-hidden' : ''"
      >
        <!-- Page Title -->
        <div v-if="title" class="mb-8">
          <h1 class="text-3xl md:text-4xl font-bold text-white">
            <span
              class="bg-gradient-to-r from-phantom-blue to-phantom-purple bg-clip-text text-transparent"
            >
              {{ title }}
            </span>
          </h1>
          <p v-if="description" class="mt-2 text-white/70">
            {{ description }}
          </p>
        </div>

        <!-- Slot for page content -->
        <div class="w-full">
          <slot />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
// Props for page title and description
defineProps({
  title: {
    type: String,
    default: "",
  },
  description: {
    type: String,
    default: "",
  },
  noScroll: {
    type: <PERSON>olean,
    default: false,
  },
});
</script>
