-- Remove pause functionality columns from sessions table
DO $$
BEGIN
    -- Drop the index first if it exists
    IF EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_sessions_paused_at'
    ) THEN
        DROP INDEX idx_sessions_paused_at;
    END IF;

    -- Remove paused_at column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'paused_at'
    ) THEN
        ALTER TABLE sessions DROP COLUMN paused_at;
    END IF;

    -- Remove total_paused_seconds column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'total_paused_seconds'
    ) THEN
        ALTER TABLE sessions DROP COLUMN total_paused_seconds;
    END IF;

    -- Remove pause_warning_sent column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'pause_warning_sent'
    ) THEN
        ALTER TABLE sessions DROP COLUMN pause_warning_sent;
    END IF;
END $$;
