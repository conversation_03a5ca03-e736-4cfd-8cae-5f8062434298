"""
Handles database migrations using PostgreSQL, including applying new migrations
and tracking applied migrations.

This script is designed to be run directly and will exit with a non-zero
status code on failure, making it suitable for use in CI/CD or Docker entrypoints.
"""

import os
import sys

import psycopg2
from app.config.db_config import DATABASE_CONFIG
from app.utils.logger import error, info
from dotenv import load_dotenv
from psycopg2 import sql

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Load environment variables (e.g., for local development)
load_dotenv()

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MIGRATIONS_DIR = os.path.join(SCRIPT_DIR, "migrations")

info(f"Looking for migrations in: {MIGRATIONS_DIR}")


def get_current_batch(cursor):
    """Retrieve the current highest batch number from the migrations table."""
    cursor.execute("SELECT COALESCE(MAX(batch), 0) FROM migrations")
    current_batch = cursor.fetchone()[0]
    return current_batch + 1


def get_applied_migrations(cursor):
    """Retrieve the list of applied migrations, creating the table if it doesn't exist."""
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS migrations (
            id SERIAL PRIMARY KEY,
            name TEXT UNIQUE NOT NULL,
            batch INTEGER NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
    )
    cursor.execute("SELECT name FROM migrations")
    return {row[0] for row in cursor.fetchall()}


def apply_migration(cursor, migration_name, sql_content, batch):
    """Apply a single migration and record it in the migrations table."""
    cursor.execute(sql.SQL(sql_content))
    cursor.execute("INSERT INTO migrations (name, batch) VALUES (%s, %s)", (migration_name, batch))
    info(f"Applied migration: {migration_name} (Batch {batch})")


def main():
    """Main function to apply new database migrations."""
    connection = None
    try:
        connection = psycopg2.connect(**DATABASE_CONFIG)
        with connection.cursor() as cursor:
            applied_migrations = get_applied_migrations(cursor)
            new_batch = get_current_batch(cursor)
            migration_files = sorted(os.listdir(MIGRATIONS_DIR))
            new_migrations = [m for m in migration_files if m not in applied_migrations]

            if not new_migrations:
                info("No new migrations to apply.")
            else:
                info(f"Found {len(new_migrations)} new migrations to apply.")
                for migration_file in new_migrations:
                    full_path = os.path.join(MIGRATIONS_DIR, migration_file)
                    with open(full_path, "r", encoding="utf-8") as f:
                        sql_content = f.read()
                    apply_migration(cursor, migration_file, sql_content, new_batch)
                connection.commit()
                info(f"Migrations applied successfully in Batch {new_batch}!")

    except (psycopg2.DatabaseError, FileNotFoundError) as e:
        error(f"CRITICAL: Error applying migrations: {e}", exception=e)
        if connection:
            connection.rollback()
        raise
    finally:
        if connection:
            connection.close()


if __name__ == "__main__":
    main()
