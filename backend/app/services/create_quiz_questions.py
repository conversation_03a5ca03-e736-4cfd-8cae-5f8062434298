"""
Quiz Question Generator Script

This module generates quiz questions across different difficulty levels using a language model.
It supports:
- Dynamic prompt loading from a YAML file
- Fetching data from the model API
- Storing generated questions in a database

Functions:
- load_yaml_prompt: Load quiz prompts from a YAML file
- fetch_quiz_data: Retrieve quiz questions from the model
- ask_for_question: Main function to generate, adjust, and save quiz questions
"""

import asyncio
import json
import os
import time

import yaml

from ..models.db_manager import get_questions_by_level, insert_question_data
from ..utils.logger import debug, error, info, warning
from .llm_client import query_model


async def load_yaml_prompt(prompt):
    """
    Load and retrieve a specific prompt from a YAML file.

    Args:
        prompt (str): The key for the desired prompt in the YAML file.

    Returns:
        str: The retrieved prompt or an empty string if an error occurs.
    """
    # Get the directory of the current file and construct path to config
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # In Docker, the app directory is /app, so we need to go up one level from /app/app
    app_root = os.path.dirname(current_dir)  # This gives us /app
    yaml_prompt_file = os.path.join(app_root, "config", "prompt.yaml")

    try:
        with open(yaml_prompt_file, "r", encoding="utf-8") as file:
            yaml_data = yaml.safe_load(file)
        return yaml_data.get("prompts", {}).get(prompt, "")
    except (FileNotFoundError, yaml.YAMLError) as e:
        error("Error loading YAML file", exception=e)
        return ""


def add_metadata_to_questions(parsed_output, level, topic):
    """Add metadata to parsed questions"""
    for item in parsed_output:
        item["Level"] = level
        item["Topic"] = topic
    return parsed_output


async def fetch_quiz_data(quiz_prompt, model_id, level, topic):
    """
    Fetch quiz data from the model for a specified level.

    Args:
        quiz_prompt (str): The formatted prompt string.
        model_id (str): The model identifier to query.
        level (str): Difficulty level (e.g., "easy", "intermediate", "advanced", "all").
        topic (str): The topic for the questions.

    Returns:
        list: Parsed quiz data as a list of dictionaries.
    """
    start_time = time.time()

    try:
        info(f"Fetching {level} level quiz data")
        model_api_response = await query_model(quiz_prompt, model_id)

        # Check if the response contains an error
        if "error" in model_api_response:
            error_msg = model_api_response.get("message", "Unknown error")
            error_code = model_api_response.get("error", "Unknown")
            error(f"Model API returned error {error_code}: {error_msg}")
            return []

        # Check if the response has the expected structure
        if "choices" not in model_api_response:
            error("'choices' key is missing in the model response.")
            debug(f"Model Response: {model_api_response}")
            return []

        # Validate that choices array is not empty
        if not model_api_response["choices"] or len(model_api_response["choices"]) == 0:
            error("Model response 'choices' array is empty.")
            debug(f"Model Response: {model_api_response}")
            return []

        # Extract content from the first choice
        choice = model_api_response["choices"][0]
        if "message" not in choice or "content" not in choice["message"]:
            error("Invalid structure in model response choices.")
            debug(f"Model Response: {model_api_response}")
            return []

        output_json_string = choice["message"]["content"]

        # Find JSON array boundaries
        start_index = output_json_string.find("[")
        end_index = output_json_string.rfind("]") + 1

        if start_index == -1 or end_index == 0:
            error("No JSON array found in model response content.")
            debug(f"Response content: {output_json_string[:200]}...")
            return []

        output_json = output_json_string[start_index:end_index]

        try:
            parsed_output = json.loads(output_json)
        except json.JSONDecodeError as json_err:
            error(f"Failed to parse JSON from model response: {json_err}")
            debug(f"JSON content: {output_json[:200]}...")
            return []

        # Validate that parsed output is a list
        if not isinstance(parsed_output, list):
            error("Parsed output is not a list.")
            debug(f"Parsed output type: {type(parsed_output)}")
            return []

        # If level is "all", each question should already have its own level
        # Otherwise, use helper function to add metadata
        if level != "all":
            parsed_output = add_metadata_to_questions(parsed_output, level, topic)
        else:
            # For "all" level, just add topic if not already present
            for item in parsed_output:
                if "Topic" not in item:
                    item["Topic"] = topic

        info(f"Fetched {len(parsed_output)} {level} level questions in {time.time() - start_time:.2f} seconds")
        return parsed_output

    except (asyncio.TimeoutError, ConnectionError) as e:
        error("Network error occurred", exception=e)
        return []

    except Exception as e:
        error("Unexpected error occurred", exception=e)
        debug(f"Model Response: {model_api_response if 'model_api_response' in locals() else 'Unavailable'}")
        return []


async def ask_for_question(quiz_name: str, topics: str, skill_id: int = None, skill_name: str = None):
    """
    Main entry point to generate and save quiz questions for the specified quiz and topics.
    Generates questions for all difficulty levels in a single request.

    Args:
        quiz_name (str): Name of the quiz to generate.
        topics (str): List of topics for which quiz questions will be generated.
        skill_id (int, optional): The skill ID to associate with these questions.
        skill_name (str, optional): The skill name to use as topic for questions.

    Returns:
        int: Number of questions generated if successful, 0 if failed.
    """
    try:
        model_id = os.getenv("MODEL_ID")
        if not model_id:
            error("MODEL_ID environment variable not set")
            return 0

        # Get question counts for each difficulty level from environment variables
        easy_questions = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))
        intermediate_questions = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))
        advanced_questions = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))

        # Calculate total questions as the sum of all difficulty levels
        total_questions = easy_questions + intermediate_questions + advanced_questions

        info(
            f"Questions to generate - Easy: {easy_questions}, "
            f"Intermediate: {intermediate_questions}, "
            f"Advanced: {advanced_questions}, Total: {total_questions}"
        )

        debug(f"Starting quiz generation: {quiz_name} for topics: {topics}")

        # Load the prompt template for quiz generation
        quiz_prompt_template = await load_yaml_prompt("questions_prompt")
        if not quiz_prompt_template:
            error("Failed to load context-gathering prompt.")
            return 0

        # Get existing questions for context
        context = []
        for level in ["easy", "intermediate", "advanced"]:
            level_questions = get_questions_by_level(quiz_name, level)
            if level_questions:
                context.extend(level_questions)

        context_str = "\n".join(context) if context else ""

        # Format the prompt with all necessary information
        quiz_prompt = quiz_prompt_template.format(no=total_questions, topics=topics, level="all", context=context_str)

        info(f"Requesting {total_questions} questions across all difficulty levels in a single request")

        # Fetch all questions in a single request
        all_questions = await fetch_quiz_data(quiz_prompt, model_id, "all", quiz_name)

        # Wait 5 seconds after LLM call
        info("Waiting 5 seconds after LLM call...")
        await asyncio.sleep(5.0)

        if not all_questions:
            error("Failed to generate questions - no questions returned from model")
            return 0

        # Process and categorize questions by level
        questions_by_level = {"easy": [], "intermediate": [], "advanced": []}

        for question in all_questions:
            # Normalize level field
            level = question.get("Level", "").lower()
            if level == "easy":
                question["Level"] = "easy"
                questions_by_level["easy"].append(question)
            elif level == "intermediate":
                question["Level"] = "intermediate"
                questions_by_level["intermediate"].append(question)
            elif level in ["advanced", "advance"]:
                question["Level"] = "advanced"
                questions_by_level["advanced"].append(question)
            else:
                warning(f"Question has invalid level: {level}. Defaulting to 'easy'.")
                question["Level"] = "easy"
                questions_by_level["easy"].append(question)

            # Ensure topic is set - use skill_name if provided, otherwise use quiz_name
            question["Topic"] = skill_name if skill_name else quiz_name

        # Limit questions to the required number per level
        level_counts = {
            "easy": easy_questions,
            "intermediate": intermediate_questions,
            "advanced": advanced_questions,
        }

        for level in questions_by_level:
            target_count = level_counts.get(level, 10)  # Default to 10 if level not found
            if len(questions_by_level[level]) > target_count:
                questions_by_level[level] = questions_by_level[level][:target_count]
            elif len(questions_by_level[level]) < target_count:
                warning(f"Only generated {len(questions_by_level[level])} {level} questions, needed {target_count}")

        # Combine all questions
        all_questions_to_insert = []
        for level in ["easy", "intermediate", "advanced"]:
            all_questions_to_insert.extend(questions_by_level[level])

        # Insert questions into database
        if all_questions_to_insert:
            insert_question_data(all_questions_to_insert, skill_id)
            questions_count = len(all_questions_to_insert)
            info(f"Successfully generated and saved {questions_count} questions")
            return questions_count  # Return the actual number of questions generated
        else:
            error("No questions were generated")
            return 0  # Return 0 instead of None for consistency

    except Exception as e:
        error("Error in ask_for_question", exception=e)
        return 0  # Return 0 instead of None for consistency
