"""
Environment variable validator to ensure all required variables are set.
"""

import os
import sys
from typing import Dict, List, Optional

from ..utils.logger import error as log_error
from ..utils.logger import info as log_info


def validate_env_vars(required_vars: List[str], optional_vars: Optional[Dict[str, str]] = None) -> bool:
    """
    Validate that all required environment variables are set.

    Args:
        required_vars: List of required environment variable names
        optional_vars: Dictionary of optional variables with default values

    Returns:
        True if all required variables are set, False otherwise
    """
    missing_vars = []

    # Check required variables
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    # Set default values for optional variables
    if optional_vars:
        for var, default in optional_vars.items():
            if not os.getenv(var):
                os.environ[var] = default

    # Report missing variables
    if missing_vars:
        log_error(
            f"Missing required environment variables: {', '.join(missing_vars)}. "
            "Please set these variables in your .env file or environment."
        )
        return False

    return True


def check_environment() -> bool:
    """
    Check if all required environment variables are set.

    Returns:
        True if all required variables are set, False otherwise
    """
    required_vars = [
        "BASE_URL",
        "API_KEY",
        "PG_USER",
        "PG_PASSWORD",
        "PG_DATABASE",
        "PG_HOST",
        "PG_PORT",
    ]

    optional_vars = {
        "MOCK_QUESTION_COUNT": "10",
        "FINAL_QUESTION_COUNT": "20",
        "TOTAL_QUESTIONS_COUNT": "30",
        "EASY_QUESTIONS_COUNT": "10",
        "INTERMEDIATE_QUESTIONS_COUNT": "10",
        "ADVANCED_QUESTIONS_COUNT": "10",
        "QUESTION_TIME": "60",
        "MODEL_ID": "qwen/qwen3-30b-a3b",
        "LOG_LEVEL": "INFO",
        "SERVER_PORT": "8000",
        "DB_MIN_CONNECTIONS": "1",
        "DB_MAX_CONNECTIONS": "10",
    }

    return validate_env_vars(required_vars, optional_vars)


if __name__ == "__main__":
    # This allows running this script directly to validate environment
    if not check_environment():
        sys.exit(1)
    log_info("Environment validation successful!")
