"""
This module provides sessions-related database operations.
"""

import random
from typing import Dict, List, Optional, Tuple

import psycopg2
import psycopg2.extras

from ...config.db_config import DATABASE_CONFIG
from ..api.middlewares.hashid_middleware import hash_ids_in_response
from ..models.db_manager import (
    calculate_total_score_for_assessment,
    get_performance_level_with_correct_total,
)
from ..utils.api_response import (
    raise_http_exception,
    success_response,
)
from ..utils.logger import (
    debug,
)


def build_session_filter_clauses(status_filter: Optional[str]) -> tuple[str, str]:
    """Build WHERE clauses for session filtering."""
    if status_filter == "pending":
        clause = "WHERE (s.status = 'pending' OR s.status = 'created' OR s.status IS NULL)"
        return clause, clause
    elif status_filter == "completed":
        clause = "WHERE (s.status = 'completed' OR s.status = 'finished')"
        return clause, clause
    else:
        # If status_filter is 'all' or None, no WHERE clause needed
        return "", ""


def get_sessions_count(cur, count_where_clause: str) -> int:
    """Get total count of sessions based on filter."""
    count_query = f"""
        SELECT COUNT(*)
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {count_where_clause}
    """
    cur.execute(count_query)
    return cur.fetchone()[0]


def get_sessions_data(cur, where_clause: str, limit: int, offset: int) -> list:
    """Get paginated sessions data."""
    main_query = f"""
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT %s OFFSET %s
    """
    cur.execute(main_query, (limit, offset))
    return [dict(row) for row in cur.fetchall()]


def get_single_session_data(cur, where_clause: str, params: Tuple) -> Optional[Dict]:
    """
    Builds and executes the main session query with a specific WHERE clause
    and parameters, returning a single result as a dictionary.

    This function combines the query definition and execution logic.

    Args:
        cur: The database cursor.
        where_clause: The SQL WHERE clause to filter the results (e.g., "WHERE s.id = %s").
        params: A tuple of parameters to be safely passed to the query.

    Returns:
        A dictionary representing the session row, or None if not found.
    """
    # The base query is now defined directly inside this function
    main_query = """
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
    """

    full_query = f"{main_query} {where_clause}"

    cur.execute(full_query, params)
    session = cur.fetchone()

    return dict(session) if session else None


def generate_unique_session_code(cur) -> str:
    """Generate a unique session code."""
    for _ in range(10):  # Max 10 attempts
        session_code = str(random.randint(100000, 999999)).zfill(6)
        cur.execute("SELECT id FROM sessions WHERE code = %s", (session_code,))
        if not cur.fetchone():
            return session_code

    raise Exception("Could not generate a unique session code after 10 attempts.")


def create_session_in_db(cur, session_code: str, user_internal_id: int, assessment_id: int) -> int:
    """Create session in database and return session ID."""
    cur.execute(
        """INSERT INTO sessions (code, user_id, assessment_id, status, created_at)
           VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
           RETURNING id""",
        (session_code, user_internal_id, assessment_id, "pending"),
    )
    return cur.fetchone()["id"]


def find_or_create_user_by_details(cur, display_name: str, email: str, external_id: str):
    """
    Finds a user by their unique email or creates a new one if not found.
    This is a robust "upsert" (update/insert) operation.

    Returns:
        int: The internal database ID of the user (either existing or newly created).
    """
    # First, try to find the user by their unique email.
    cur.execute("SELECT id FROM users WHERE email = %s", (email,))
    user = cur.fetchone()
    if user:
        return user["id"]

    # If the user doesn't exist, create a new one with all required fields.
    cur.execute(
        """
        INSERT INTO users (display_name, email, external_id, created_at, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
        """,
        (display_name, email, external_id),
    )
    new_user = cur.fetchone()
    if not new_user:
        # This would indicate a serious database issue.
        raise Exception(f"Failed to create user with email {email}")
    return new_user["id"]


def validate_assessment_exists(cur, assessment_id: int):
    """Validate that assessment exists in database."""
    cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
    if not cur.fetchone():
        raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")


def handle_completed_session(session_details: dict):
    """Handle already completed session."""
    debug("Session already completed, returning existing data")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                "SELECT score, completed_at FROM sessions WHERE id = %s",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                data = {
                    "session_id": session_details["session_id"],
                    "obtained_score": result["score"] or 0,
                    "status": "completed",
                    "message": "Session was already completed",
                }
                hashed_data = hash_ids_in_response(data)
                return success_response(data=hashed_data, message="Session was already completed")

    return success_response(
        data={"status": "completed", "message": "Session was already completed"},
        message="Session was already completed",
    )


def handle_expired_session(session_details: dict):
    """Handle expired session by completing it."""
    debug("Session expired, but attempting to complete anyway")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed', completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s
                   RETURNING id""",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                conn.commit()
                debug(f"Successfully completed expired session {session_details['session_id']}")
                return success_response(
                    data={"status": "completed", "message": "Expired session completed"},
                    message="Session completed successfully",
                )


def calculate_session_scores(session_details: dict) -> tuple[float, float, str]:
    """Calculate obtained score, total possible score, and performance level."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            # Calculate obtained score from user_answers
            cur.execute(
                "SELECT COALESCE(SUM(score), 0) as obtained_score FROM user_answers WHERE session_id = %s",
                (session_details["session_id"],),
            )
            score_result = cur.fetchone()
            obtained_score = score_result["obtained_score"] if score_result else 0

            # Calculate correct total possible score based on assessment mode
            assessment_id = session_details["assessment_id"]
            session_id = session_details["session_id"]
            total_possible_score = calculate_total_score_for_assessment(assessment_id, session_id)

            # Calculate performance level with correct total score
            performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_id)

            return obtained_score, total_possible_score, performance_level


def complete_session_in_db(session_details: dict, obtained_score: float):
    """Complete the session in database."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed',
                       score = %s,
                       completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s AND status = 'in_progress'
                   RETURNING id""",
                (obtained_score, session_details["session_id"]),
            )

            updated_session = cur.fetchone()
            if not updated_session:
                raise_http_exception(status_code=400, detail="Session could not be completed.")

            conn.commit()


# --- Helper Function to avoid repeating the main query ---


def _get_session_base_query() -> str:
    """Returns the base SELECT statement for session details to keep queries DRY."""
    return """
        SELECT s.id, s.code as session_code, s.user_id, u.external_id as username, u.display_name,
               s.assessment_id, s.score, s.status as session_status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name, a.is_final, a.question_selection_mode, a.duration_minutes
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
    """


# --- Functions for specific data retrieval and updates ---


def get_session_for_start_or_validation(cur, session_code: str) -> Optional[Dict]:
    """
    Gets comprehensive session details by its 6-digit code.
    Used for validation and starting a session. Joins sessions, users, and assessments.
    """
    base_query = _get_session_base_query()
    query = f"{base_query} WHERE s.code = %s"
    cur.execute(query, (session_code,))
    session = cur.fetchone()
    # Note: `get_session_and_assessment_details_by_code` is now replaced by this.
    return dict(session) if session else None


def start_session_in_db(cur, session_id: int) -> int:
    """
    Updates a session's status to 'in_progress' and sets the start time.
    Returns the number of rows affected (should be 1 on success, 0 on failure).
    """
    query = """
        UPDATE sessions
        SET status = 'in_progress', started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
        WHERE id = %s AND status = 'pending'
    """
    cur.execute(query, (session_id,))
    return cur.rowcount


def get_completed_session_for_results(cur, session_identifier: str) -> Optional[Dict]:
    """
    Gets a completed session's details by its 6-digit code or numeric ID.
    """
    base_query = _get_session_base_query()

    if len(session_identifier) == 6 and session_identifier.isdigit():
        # It's a 6-digit session code
        where_clause = "WHERE s.code = %s AND s.status = 'completed'"
        params = (session_identifier,)
    elif session_identifier.isdigit():
        # It's a numeric ID
        where_clause = "WHERE s.id = %s AND s.status = 'completed'"
        params = (int(session_identifier),)
    else:
        # It's a hash, decode it first (logic remains in the endpoint)
        # This function assumes the ID is already decoded if it's not a code/numeric ID.
        where_clause = "WHERE s.id = %s AND s.status = 'completed'"
        # You would decode the hash *before* calling this function.
        # For simplicity, we assume the endpoint will handle decoding and pass a numeric ID.
        return None  # Or handle decoded ID if you pass it in

    query = f"{base_query} {where_clause}"
    cur.execute(query, params)
    session = cur.fetchone()
    return dict(session) if session else None


def get_session_answers_for_results(cur, session_internal_id: int) -> List[Dict]:
    """
    Gets all questions, user answers, and correct answers for a given session.
    """
    query = """
        SELECT ua.question_id, ua.user_answer, ua.is_correct, ua.score,
               q.question, q.options, q.answer as correct_answer_key, q.level
        FROM user_answers ua
        JOIN questions q ON ua.question_id = q.que_id
        WHERE ua.session_id = %s
        ORDER BY ua.created_at
    """
    cur.execute(query, (session_internal_id,))
    return [dict(row) for row in cur.fetchall()]


def get_session_user_details(cur, session_code: str) -> Optional[Dict]:
    """

    Gets user and assessment details for a given session code in a single query.
    """
    query = """
        SELECT
            u.external_id,
            u.display_name,
            a.id as assessment_id,
            a.name as assessment_name,
            a.is_final,
            s.status as session_status
        FROM sessions s
        JOIN users u ON s.user_id = u.id
        JOIN assessments a ON s.assessment_id = a.id
        WHERE s.code = %s
    """
    cur.execute(query, (session_code,))
    result = cur.fetchone()
    return dict(result) if result else None


def get_user_id_by_email(cur, email: str) -> Optional[int]:
    """
    Fetches a user's internal integer ID from their email address.

    Args:
        cur: The database cursor.
        email: The user's email address.

    Returns:
        The integer user ID, or None if not found.
    """
    query = "SELECT id FROM users WHERE email = %s"
    cur.execute(query, (email,))
    result = cur.fetchone()
    return result["id"] if result else None


def get_sessions_by_user_id(cur, user_id: int) -> List[Dict]:
    """
    Fetches all sessions associated with a specific user ID, ordered by creation date.
    This reuses the main session query for consistency.

    Args:
        cur: The database cursor.
        user_id: The internal integer ID of the user.

    Returns:
        A list of dictionaries, where each dictionary represents a session.
    """
    base_query = _get_session_base_query()
    # Append the specific filter and ordering for this use case
    full_query = f"{base_query} WHERE s.user_id = %s ORDER BY s.created_at DESC"
    cur.execute(full_query, (user_id,))
    return [dict(row) for row in cur.fetchall()]
